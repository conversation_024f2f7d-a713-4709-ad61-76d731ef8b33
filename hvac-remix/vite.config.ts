import path from "node:path";
import { vitePlugin as remix } from "@remix-run/dev";
import { defineConfig } from "vite";
import tsconfigPaths from "vite-tsconfig-paths";

export default defineConfig({
  plugins: [
    remix({
      future: {
        v3_fetcherPersist: true,
        v3_lazyRouteDiscovery: true,
        v3_relativeSplatPath: true,
        v3_singleFetch: true,
        v3_throwAbortReason: true,
      },
    }),
    tsconfigPaths(),
  ],
  resolve: {
    alias: {
      "~": path.resolve(__dirname, "app"),
    },
  },
  server: {
    port: 3000,
  },
  build: {
    target: "esnext",
  },
  ssr: {
    noExternal: ["~", "victory-vendor"],
  },
  optimizeDeps: {
    exclude: ["@remix-run/remix"],
    include: ["victory-vendor", "recharts"],
  },
});
