/**
 * ocr.server.ts - GoBackend-Kratos Integration
 * Provides ocr functionality through API
 */

// Temporary mock implementation until GoBackend-Kratos is fully integrated
console.log('ocr.server.ts loaded - using mock implementation');

// Export placeholder functions to prevent import errors
export const placeholder = true;

// Add specific exports based on file type

export async function processDocument(documentUrl: string): Promise<{ text: string; confidence: number }> {
  console.log('Mock OCR processing:', documentUrl);
  return { text: 'Mock extracted text', confidence: 0.95 };
}

export async function extractInvoiceData(documentUrl: string): Promise<any> {
  console.log('Mock invoice extraction:', documentUrl);
  return { amount: 1000, date: new Date(), vendor: 'Mock Vendor' };
}

export async function processInvoice(invoiceId: string, documentUrl: string): Promise<any> {
  console.log('Mock invoice processing:', invoiceId, documentUrl);
  return { success: true, extractedData: { amount: 1000, date: new Date(), vendor: 'Mock Vendor' } };
}

export async function processServiceReport(reportId: string, documentUrl: string): Promise<any> {
  console.log('Mock service report processing:', reportId, documentUrl);
  return { success: true, extractedData: { workPerformed: 'Mock work', duration: 2, technician: 'Mock Tech' } };
}

