import { useEffect, useRef } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "~/components/ui/card";

// Dynamically import GSAP only on client side
let gsap: any = null;
if (typeof window !== 'undefined') {
  import('gsap').then((module) => {
    gsap = module.default;
  }).catch((error) => {
    console.warn('GSAP failed to load:', error);
  });
}

// Helper function to safely use GSAP
const safeGsap = {
  to: (target: any, vars: any) => {
    if (gsap && typeof gsap.to === 'function') {
      return gsap.to(target, vars);
    }
    return null;
  },
  fromTo: (target: any, fromVars: any, toVars: any) => {
    if (gsap && typeof gsap.fromTo === 'function') {
      return gsap.fromTo(target, fromVars, toVars);
    }
    return null;
  },
  from: (target: any, vars: any) => {
    if (gsap && typeof gsap.from === 'function') {
      return gsap.from(target, vars);
    }
    return null;
  }
};

interface ServiceOrdersByMonth {
  month: string;
  count: number;
}

interface TopCustomer {
  id: string;
  name: string;
  _count: {
    serviceOrders: number;
  };
}

interface TopDevice {
  id: string;
  name: string;
  model: string | null;
  _count: {
    serviceOrders: number;
  };
}

interface ServiceOrderType {
  type: string;
  _count: {
    id: number;
  };
}

interface DataExplorationVisualizationProps {
  serviceOrdersByMonth: ServiceOrdersByMonth[];
  topCustomers: TopCustomer[];
  topDevices: TopDevice[];
  serviceOrderTypes: ServiceOrderType[];
}

export function DataExplorationVisualization({
  serviceOrdersByMonth,
  topCustomers,
  topDevices,
  serviceOrderTypes,
}: DataExplorationVisualizationProps) {
  const monthlyChartRef = useRef<HTMLDivElement>(null);
  const customersChartRef = useRef<HTMLDivElement>(null);
  const devicesChartRef = useRef<HTMLDivElement>(null);
  const typesChartRef = useRef<HTMLDivElement>(null);

  // Type colors
  const typeColors: Record<string, string> = {
    SERVICE: "#3b82f6",
    INSTALLATION: "#10b981",
    MAINTENANCE: "#f59e0b",
    REPAIR: "#ef4444",
    INSPECTION: "#8b5cf6",
  };

  useEffect(() => {
    // Create monthly chart
    if (monthlyChartRef.current && serviceOrdersByMonth.length > 0) {
      // Clear previous chart
      monthlyChartRef.current.innerHTML = "";

      // Create tooltip
      const tooltip = document.createElement("div");
      tooltip.className = "absolute bg-popover text-popover-foreground p-2 rounded shadow-lg text-sm z-10 pointer-events-none opacity-0";
      tooltip.style.transition = "opacity 0.2s";
      tooltip.style.left = "-1000px";
      monthlyChartRef.current.appendChild(tooltip);

      const chartContainer = document.createElement("div");
      chartContainer.className = "relative h-full";
      monthlyChartRef.current.appendChild(chartContainer);

      // Add controls
      const controls = document.createElement("div");
      controls.className = "absolute top-0 right-0 flex space-x-2";

      // Add export button
      const exportBtn = document.createElement("button");
      exportBtn.className = "text-xs px-2 py-1 bg-muted rounded hover:bg-muted/80 transition-colors";
      exportBtn.textContent = "Export Data";
      exportBtn.title = "Export chart data as CSV";
      exportBtn.onclick = () => {
        // Create CSV content
        const headers = ["Month", "Service Orders"];
        const rows = serviceOrdersByMonth.map(d => [d.month, d.count]);
        const csvContent = [
          headers.join(","),
          ...rows.map(row => row.join(","))
        ].join("\n");

        // Create and trigger download
        const blob = new Blob([csvContent], { type: "text/csv" });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = "service-orders-by-month.csv";
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      };

      controls.appendChild(exportBtn);
      chartContainer.appendChild(controls);

      const chartWidth = chartContainer.clientWidth || 500;
      const chartHeight = 200;
      const padding = { top: 20, right: 30, bottom: 40, left: 50 };
      const innerWidth = chartWidth - padding.left - padding.right;
      const innerHeight = chartHeight - padding.top - padding.bottom;

      const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
      svg.setAttribute("width", chartWidth.toString());
      svg.setAttribute("height", chartHeight.toString());
      svg.setAttribute("class", "overflow-visible");

      const g = document.createElementNS("http://www.w3.org/2000/svg", "g");
      g.setAttribute("transform", `translate(${padding.left}, ${padding.top})`);
      svg.appendChild(g);

      // Create gradient for bars
      const defs = document.createElementNS("http://www.w3.org/2000/svg", "defs");
      const gradient = document.createElementNS("http://www.w3.org/2000/svg", "linearGradient");
      gradient.setAttribute("id", "bar-gradient");
      gradient.setAttribute("x1", "0%");
      gradient.setAttribute("y1", "0%");
      gradient.setAttribute("x2", "0%");
      gradient.setAttribute("y2", "100%");

      const stop1 = document.createElementNS("http://www.w3.org/2000/svg", "stop");
      stop1.setAttribute("offset", "0%");
      stop1.setAttribute("stop-color", "#3b82f6");

      const stop2 = document.createElementNS("http://www.w3.org/2000/svg", "stop");
      stop2.setAttribute("offset", "100%");
      stop2.setAttribute("stop-color", "#60a5fa");

      gradient.appendChild(stop1);
      gradient.appendChild(stop2);
      defs.appendChild(gradient);
      svg.appendChild(defs);

      // Create scales
      const maxCount = Math.max(...serviceOrdersByMonth.map(d => d.count));
      const barWidth = innerWidth / serviceOrdersByMonth.length - 10;

      // Add grid lines
      const gridGroup = document.createElementNS("http://www.w3.org/2000/svg", "g");
      gridGroup.setAttribute("class", "grid-lines");

      // Create 5 horizontal grid lines
      const gridLineCount = 5;
      for (let i = 0; i <= gridLineCount; i++) {
        const y = innerHeight * (i / gridLineCount);
        const gridLine = document.createElementNS("http://www.w3.org/2000/svg", "line");
        gridLine.setAttribute("x1", "0");
        gridLine.setAttribute("y1", y.toString());
        gridLine.setAttribute("x2", innerWidth.toString());
        gridLine.setAttribute("y2", y.toString());
        gridLine.setAttribute("stroke", "#e5e7eb");
        gridLine.setAttribute("stroke-width", "1");
        gridLine.setAttribute("stroke-dasharray", "4");

        // Add y-axis label
        const label = document.createElementNS("http://www.w3.org/2000/svg", "text");
        label.setAttribute("x", "-5");
        label.setAttribute("y", y.toString());
        label.setAttribute("text-anchor", "end");
        label.setAttribute("dominant-baseline", "middle");
        label.setAttribute("font-size", "10");
        label.setAttribute("fill", "currentColor");
        label.textContent = Math.round(maxCount * (1 - i / gridLineCount)).toString();

        gridGroup.appendChild(gridLine);
        gridGroup.appendChild(label);
      }

      g.appendChild(gridGroup);

      // Create axes
      const xAxis = document.createElementNS("http://www.w3.org/2000/svg", "g");
      xAxis.setAttribute("transform", `translate(0, ${innerHeight})`);

      // Add x-axis label
      const xAxisLabel = document.createElementNS("http://www.w3.org/2000/svg", "text");
      xAxisLabel.setAttribute("x", (innerWidth / 2).toString());
      xAxisLabel.setAttribute("y", "35");
      xAxisLabel.setAttribute("text-anchor", "middle");
      xAxisLabel.setAttribute("font-size", "12");
      xAxisLabel.setAttribute("fill", "currentColor");
      xAxisLabel.textContent = "Month";
      xAxis.appendChild(xAxisLabel);

      // Add y-axis label
      const yAxisLabel = document.createElementNS("http://www.w3.org/2000/svg", "text");
      yAxisLabel.setAttribute("transform", `rotate(-90) translate(${-innerHeight/2}, ${-35})`);
      yAxisLabel.setAttribute("text-anchor", "middle");
      yAxisLabel.setAttribute("font-size", "12");
      yAxisLabel.setAttribute("fill", "currentColor");
      yAxisLabel.textContent = "Service Orders";
      g.appendChild(yAxisLabel);

      serviceOrdersByMonth.forEach((d, i) => {
        const x = i * (barWidth + 10) + barWidth / 2;

        const text = document.createElementNS("http://www.w3.org/2000/svg", "text");
        text.setAttribute("x", x.toString());
        text.setAttribute("y", "20");
        text.setAttribute("text-anchor", "middle");
        text.setAttribute("font-size", "10");
        text.setAttribute("fill", "currentColor");
        text.textContent = d.month;

        xAxis.appendChild(text);
      });

      g.appendChild(xAxis);

      // Create interactive bars with animations
      serviceOrdersByMonth.forEach((d, i) => {
        const barHeight = (d.count / maxCount) * innerHeight;
        const x = i * (barWidth + 10);
        const y = innerHeight - barHeight;

        // Create group for bar and related elements
        const barGroup = document.createElementNS("http://www.w3.org/2000/svg", "g");
        barGroup.setAttribute("class", "bar-group");

        // Create rectangle for hover area (invisible, larger than the bar)
        const hoverArea = document.createElementNS("http://www.w3.org/2000/svg", "rect");
        hoverArea.setAttribute("x", (x - 5).toString());
        hoverArea.setAttribute("y", "0");
        hoverArea.setAttribute("width", (barWidth + 10).toString());
        hoverArea.setAttribute("height", innerHeight.toString());
        hoverArea.setAttribute("fill", "transparent");

        // Create the actual bar
        const rect = document.createElementNS("http://www.w3.org/2000/svg", "rect");
        rect.setAttribute("x", x.toString());
        rect.setAttribute("y", innerHeight.toString());
        rect.setAttribute("width", barWidth.toString());
        rect.setAttribute("height", "0");
        rect.setAttribute("fill", "url(#bar-gradient)");
        rect.setAttribute("rx", "4");
        rect.style.filter = "drop-shadow(0 1px 2px rgba(0,0,0,0.1))";
        rect.setAttribute("class", "bar");

        // Create value text
        const text = document.createElementNS("http://www.w3.org/2000/svg", "text");
        text.setAttribute("x", (x + barWidth / 2).toString());
        text.setAttribute("y", (y - 5).toString());
        text.setAttribute("text-anchor", "middle");
        text.setAttribute("font-size", "10");
        text.setAttribute("fill", "currentColor");
        text.textContent = d.count.toString();
        text.style.opacity = "0";

        // Add tooltip and hover effects
        const handleMouseEnter = (e: MouseEvent) => {
          // Update tooltip
          tooltip.innerHTML = `
            <div class="font-medium">${d.month}</div>
            <div>Service Orders: <span class="font-medium">${d.count}</span></div>
          `;
          tooltip.style.opacity = "1";
          tooltip.style.left = `${e.pageX}px`;
          tooltip.style.top = `${e.pageY - 40}px`;

          // Highlight bar
          safeGsap.to(rect, {
            fill: "#2563eb",
            scale: 1.05,
            transformOrigin: "bottom",
            duration: 0.2,
          });

          // Enlarge text
          safeGsap.to(text, {
            scale: 1.2,
            fontWeight: "bold",
            duration: 0.2,
          });
        };

        const handleMouseMove = (e: MouseEvent) => {
          tooltip.style.left = `${e.pageX}px`;
          tooltip.style.top = `${e.pageY - 40}px`;
        };

        const handleMouseLeave = () => {
          tooltip.style.opacity = "0";

          safeGsap.to(rect, {
            fill: "url(#bar-gradient)",
            scale: 1,
            duration: 0.2,
          });

          safeGsap.to(text, {
            scale: 1,
            fontWeight: "normal",
            duration: 0.2,
          });
        };

        hoverArea.addEventListener("mouseenter", handleMouseEnter as EventListener);
        hoverArea.addEventListener("mousemove", handleMouseMove as EventListener);
        hoverArea.addEventListener("mouseleave", handleMouseLeave as EventListener);

        barGroup.appendChild(rect);
        barGroup.appendChild(text);
        barGroup.appendChild(hoverArea);
        g.appendChild(barGroup);

        // Animate the bar with improved animation
        safeGsap.to(rect, {
          y,
          height: barHeight,
          duration: 1.2,
          delay: i * 0.08,
          ease: "elastic.out(1, 0.75)",
        });

        safeGsap.to(text, {
          opacity: 1,
          duration: 0.5,
          delay: i * 0.08 + 0.7,
        });
      });

      chartContainer.appendChild(svg);
    }

    // Create customers chart
    if (customersChartRef.current && topCustomers.length > 0) {
      // Clear previous chart
      customersChartRef.current.innerHTML = "";

      // Create header with controls
      const headerContainer = document.createElement("div");
      headerContainer.className = "flex justify-between items-center mb-4";

      const totalServiceOrders = topCustomers.reduce((sum, c) => sum + c._count.serviceOrders, 0);
      const statsLabel = document.createElement("div");
      statsLabel.className = "text-xs text-muted-foreground";
      statsLabel.textContent = `${totalServiceOrders} total service orders across top customers`;

      const exportBtn = document.createElement("button");
      exportBtn.className = "text-xs px-2 py-1 bg-muted rounded hover:bg-muted/80 transition-colors";
      exportBtn.textContent = "Export Data";

      exportBtn.onclick = () => {
        // Create CSV content
        const headers = ["Customer ID", "Customer Name", "Service Orders"];
        const rows = topCustomers.map(c => [c.id, c.name, c._count.serviceOrders]);
        const csvContent = [
          headers.join(","),
          ...rows.map(row => row.join(","))
        ].join("\n");

        // Create and trigger download
        const blob = new Blob([csvContent], { type: "text/csv" });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = "top-customers.csv";
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      };

      headerContainer.appendChild(statsLabel);
      headerContainer.appendChild(exportBtn);
      customersChartRef.current.appendChild(headerContainer);

      // Create chart content
      const chartContent = document.createElement("div");
      chartContent.className = "space-y-3 relative";

      const maxCount = Math.max(...topCustomers.map(c => c._count.serviceOrders));

      topCustomers.forEach((customer, index) => {
        const barContainer = document.createElement("div");
        barContainer.className = "flex items-center hover:bg-muted/50 p-2 rounded transition-colors duration-200 cursor-pointer";

        const label = document.createElement("div");
        label.className = "w-32 text-sm font-medium truncate mr-3";
        label.title = customer.name;
        label.textContent = customer.name;

        const barWrapper = document.createElement("div");
        barWrapper.className = "flex-1 bg-muted rounded-full h-8 overflow-hidden relative";

        // Add percentage indicator
        const percentage = Math.round((customer._count.serviceOrders / maxCount) * 100);
        const percentageLabel = document.createElement("div");
        percentageLabel.className = "absolute right-2 top-1/2 -translate-y-1/2 text-xs opacity-50 z-10";
        percentageLabel.textContent = `${percentage}%`;
        barWrapper.appendChild(percentageLabel);

        const bar = document.createElement("div");
        bar.className = "h-8 rounded-full flex items-center justify-end px-3 text-white text-sm relative";
        bar.style.width = "0%";
        bar.style.background = "linear-gradient(90deg, #3b82f6, #60a5fa)";
        bar.textContent = customer._count.serviceOrders.toString();

        // Add shimmer effect
        const shimmer = document.createElement("div");
        shimmer.className = "absolute inset-0 w-full h-full";
        shimmer.style.background = "linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent)";
        shimmer.style.backgroundSize = "200% 100%";
        shimmer.style.animation = "shimmer 2s infinite";
        bar.appendChild(shimmer);

        const style = document.createElement("style");
        style.textContent = `
          @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
          }
        `;
        document.head.appendChild(style);

        // Add hover effect
        barContainer.addEventListener("mouseenter", () => {
          safeGsap.to(bar, {
            scale: 1.02,
            brightness: 1.2,
            duration: 0.3,
          });
        });

        barContainer.addEventListener("mouseleave", () => {
          safeGsap.to(bar, {
            scale: 1,
            brightness: 1,
            duration: 0.3,
          });
        });

        barWrapper.appendChild(bar);
        barContainer.appendChild(label);
        barContainer.appendChild(barWrapper);
        chartContent.appendChild(barContainer);

        // Animate the bar with improved animation
        safeGsap.to(bar, {
          width: `${(customer._count.serviceOrders / maxCount) * 100}%`,
          duration: 1.2,
          delay: index * 0.15,
          ease: "expo.out",
        });
      });

      customersChartRef.current.appendChild(chartContent);
    }

    // Create devices chart
    if (devicesChartRef.current && topDevices.length > 0) {
      // Clear previous chart
      devicesChartRef.current.innerHTML = "";

      // Create tooltip for device details
      const tooltip = document.createElement("div");
      tooltip.className = "absolute bg-popover text-popover-foreground p-3 rounded shadow-lg text-sm z-10 pointer-events-none opacity-0 transition-opacity duration-200";
      tooltip.style.left = "-1000px";
      devicesChartRef.current.appendChild(tooltip);

      // Create header with controls
      const headerContainer = document.createElement("div");
      headerContainer.className = "flex justify-between items-center mb-4";

      const statsLabel = document.createElement("div");
      statsLabel.className = "text-xs text-muted-foreground";
      statsLabel.textContent = `Top ${topDevices.length} devices requiring service`;

      const exportBtn = document.createElement("button");
      exportBtn.className = "text-xs px-2 py-1 bg-muted rounded hover:bg-muted/80 transition-colors";
      exportBtn.textContent = "Export Data";

      exportBtn.onclick = () => {
        // Create CSV content
        const headers = ["Device ID", "Device Name", "Model", "Service Orders"];
        const rows = topDevices.map(d => [d.id, d.name, d.model || 'N/A', d._count.serviceOrders]);
        const csvContent = [
          headers.join(","),
          ...rows.map(row => row.join(","))
        ].join("\n");

        // Create and trigger download
        const blob = new Blob([csvContent], { type: "text/csv" });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = "top-devices.csv";
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      };

      headerContainer.appendChild(statsLabel);
      headerContainer.appendChild(exportBtn);
      devicesChartRef.current.appendChild(headerContainer);

      // Create chart content
      const chartContent = document.createElement("div");
      chartContent.className = "space-y-3 relative";

      const maxCount = Math.max(...topDevices.map(d => d._count.serviceOrders));

      topDevices.forEach((device, index) => {
        const barContainer = document.createElement("div");
        barContainer.className = "flex items-center hover:bg-muted/50 p-2 rounded transition-colors duration-200 cursor-pointer group";

        // Add device icon
        const iconContainer = document.createElement("div");
        iconContainer.className = "w-6 h-6 mr-2 flex items-center justify-center bg-green-100 rounded text-green-700";
        iconContainer.innerHTML = `
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M5 12h14M12 5v14" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        `;

        const label = document.createElement("div");
        label.className = "w-24 text-sm font-medium truncate";
        label.title = `${device.name}${device.model ? ` (${device.model})` : ""}`;
        label.textContent = device.name;

        // Add model info if available
        if (device.model) {
          const modelInfo = document.createElement("div");
          modelInfo.className = "text-xs text-muted-foreground truncate w-20 opacity-0 group-hover:opacity-100 transition-opacity";
          modelInfo.textContent = device.model;
          label.appendChild(modelInfo);
        }

        const barWrapper = document.createElement("div");
        barWrapper.className = "flex-1 bg-muted rounded-full h-8 overflow-hidden";

        const bar = document.createElement("div");
        bar.className = "h-8 rounded-full flex items-center justify-end px-3 text-white text-sm";
        bar.style.width = "0%";
        bar.style.background = "linear-gradient(90deg, #059669, #10b981)";
        bar.textContent = device._count.serviceOrders.toString();

        // Show tooltip with device details on hover
        barContainer.addEventListener("mouseenter", (e: MouseEvent) => {
          tooltip.innerHTML = `
            <div class="font-medium mb-1">${device.name}</div>
            ${device.model ? `<div class="text-xs mb-2">Model: ${device.model}</div>` : ''}
            <div>Service Orders: <span class="font-medium">${device._count.serviceOrders}</span></div>
            <div class="text-xs text-muted-foreground mt-1">Device ID: ${device.id}</div>
          `;
          tooltip.style.opacity = "1";
          tooltip.style.left = `${e.pageX}px`;
          tooltip.style.top = `${e.pageY - 40}px`;

          // Highlight bar
          safeGsap.to(bar, {
            scale: 1.02,
            brightness: 1.1,
            duration: 0.2,
          });
        });

        barContainer.addEventListener("mousemove", (e: MouseEvent) => {
          tooltip.style.left = `${e.pageX}px`;
          tooltip.style.top = `${e.pageY - 40}px`;
        });

        barContainer.addEventListener("mouseleave", () => {
          tooltip.style.opacity = "0";

          safeGsap.to(bar, {
            scale: 1,
            brightness: 1,
            duration: 0.2,
          });
        });

        barWrapper.appendChild(bar);
        barContainer.appendChild(iconContainer);
        barContainer.appendChild(label);
        barContainer.appendChild(barWrapper);
        chartContent.appendChild(barContainer);

        // Animate the bar with improved animation
        safeGsap.fromTo(
          barContainer,
          { x: -20, opacity: 0 },
          {
            x: 0,
            opacity: 1,
            duration: 0.5,
            delay: index * 0.1,
            ease: "power2.out"
          }
        );

        safeGsap.to(bar, {
          width: `${(device._count.serviceOrders / maxCount) * 100}%`,
          duration: 1,
          delay: index * 0.1 + 0.3,
          ease: "back.out(1.2)",
        });
      });

      devicesChartRef.current.appendChild(chartContent);
    }

    // Create types chart
    if (typesChartRef.current && serviceOrderTypes.length > 0) {
      // Clear previous chart
      typesChartRef.current.innerHTML = "";

      // Create chart header with export option
      const headerContainer = document.createElement("div");
      headerContainer.className = "flex justify-between items-center mb-4";

      const total = serviceOrderTypes.reduce((sum, t) => sum + t._count.id, 0);
      const statsLabel = document.createElement("div");
      statsLabel.className = "text-xs text-muted-foreground";
      statsLabel.textContent = `${total} total service orders by type`;

      const exportBtn = document.createElement("button");
      exportBtn.className = "text-xs px-2 py-1 bg-muted rounded hover:bg-muted/80 transition-colors";
      exportBtn.textContent = "Export Data";

      exportBtn.onclick = () => {
        // Create CSV content
        const headers = ["Type", "Count", "Percentage"];
        const rows = serviceOrderTypes.map(t => [
          t.type,
          t._count.id,
          `${Math.round((t._count.id / total) * 100)}%`
        ]);
        const csvContent = [
          headers.join(","),
          ...rows.map(row => row.join(","))
        ].join("\n");

        // Create and trigger download
        const blob = new Blob([csvContent], { type: "text/csv" });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = "service-order-types.csv";
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      };

      headerContainer.appendChild(statsLabel);
      headerContainer.appendChild(exportBtn);

      // Create tooltip element
      const tooltip = document.createElement("div");
      tooltip.className = "absolute bg-popover text-popover-foreground p-3 rounded shadow-lg text-sm z-10 pointer-events-none opacity-0 transition-opacity duration-200";
      tooltip.style.left = "-1000px";

      // Create chart container
      const chartContainer = document.createElement("div");
      chartContainer.className = "relative flex flex-col items-center";
      chartContainer.appendChild(headerContainer);

      const chartSize = Math.min(typesChartRef.current.clientWidth, 240);
      const centerX = chartSize / 2;
      const centerY = chartSize / 2;
      const radius = chartSize / 2 - 15;

      const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
      svg.setAttribute("width", chartSize.toString());
      svg.setAttribute("height", chartSize.toString());
      svg.setAttribute("viewBox", `0 0 ${chartSize} ${chartSize}`);
      svg.style.filter = "drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1))";

      // Create a defs section for gradients
      const defs = document.createElementNS("http://www.w3.org/2000/svg", "defs");
      svg.appendChild(defs);

      // Create gradients for each type
      Object.entries(typeColors).forEach(([type, color]) => {
        const gradientId = `gradient-${type}`;
        const gradient = document.createElementNS("http://www.w3.org/2000/svg", "linearGradient");
        gradient.setAttribute("id", gradientId);
        gradient.setAttribute("x1", "0%");
        gradient.setAttribute("y1", "0%");
        gradient.setAttribute("x2", "100%");
        gradient.setAttribute("y2", "100%");

        const stop1 = document.createElementNS("http://www.w3.org/2000/svg", "stop");
        stop1.setAttribute("offset", "0%");
        stop1.setAttribute("stop-color", color);

        const stop2 = document.createElementNS("http://www.w3.org/2000/svg", "stop");
        stop2.setAttribute("offset", "100%");
        // Create a slightly darker color for the gradient
        const darkerColor = color.replace(/[0-9a-f]{2}$/i, '99');
        stop2.setAttribute("stop-color", darkerColor);

        gradient.appendChild(stop1);
        gradient.appendChild(stop2);
        defs.appendChild(gradient);
      });

      // Create center circle for 3D effect
      const centerCircle = document.createElementNS("http://www.w3.org/2000/svg", "circle");
      centerCircle.setAttribute("cx", centerX.toString());
      centerCircle.setAttribute("cy", centerY.toString());
      centerCircle.setAttribute("r", (radius * 0.4).toString());
      centerCircle.setAttribute("fill", "#f8f9fa");
      centerCircle.setAttribute("opacity", "0.7");
      centerCircle.style.filter = "drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1))";

      let startAngle = 0;
      const legend = document.createElement("div");
      legend.className = "mt-6 grid grid-cols-2 gap-3 w-full";

      const paths: SVGPathElement[] = [];

      serviceOrderTypes.forEach((typeCount, index) => {
        const percentage = typeCount._count.id / total;
        const endAngle = startAngle + percentage * 360;

        // Calculate the SVG arc path
        const startRad = (startAngle - 90) * Math.PI / 180;
        const endRad = (endAngle - 90) * Math.PI / 180;

        const midAngleRad = (startRad + endRad) / 2;

        const x1 = centerX + radius * Math.cos(startRad);
        const y1 = centerY + radius * Math.sin(startRad);
        const x2 = centerX + radius * Math.cos(endRad);
        const y2 = centerY + radius * Math.sin(endRad);

        const largeArcFlag = percentage > 0.5 ? 1 : 0;

        const pathData = [
          `M ${centerX} ${centerY}`,
          `L ${x1} ${y1}`,
          `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
          "Z",
        ].join(" ");

        const path = document.createElementNS("http://www.w3.org/2000/svg", "path");
        path.setAttribute("d", pathData);

        // Use gradient instead of solid color
        const gradientId = `gradient-${typeCount.type}`;
        if (document.getElementById(gradientId)) {
          path.setAttribute("fill", `url(#${gradientId})`);
        } else {
          path.setAttribute("fill", typeColors[typeCount.type] || "#6b7280");
        }

        path.setAttribute("stroke", "white");
        path.setAttribute("stroke-width", "1.5");
        path.setAttribute("data-type", typeCount.type);
        path.setAttribute("data-count", typeCount._count.id.toString());
        path.setAttribute("data-percentage", `${Math.round(percentage * 100)}`);

        // Store original position for animations
        path.dataset.cx = centerX.toString();
        path.dataset.cy = centerY.toString();

        // Add hover effects with tooltip
        path.addEventListener("mouseenter", (e: MouseEvent) => {
          tooltip.innerHTML = `
            <div class="font-medium">${typeCount.type}</div>
            <div>Count: <span class="font-medium">${typeCount._count.id}</span></div>
            <div>Percentage: <span class="font-medium">${Math.round(percentage * 100)}%</span></div>
          `;
          tooltip.style.opacity = "1";
          tooltip.style.left = `${e.pageX}px`;
          tooltip.style.top = `${e.pageY - 40}px`;

          // Segment pop out effect
          const translateX = Math.cos(midAngleRad) * 10;
          const translateY = Math.sin(midAngleRad) * 10;

          safeGsap.to(path, {
            transform: `translate(${translateX}px, ${translateY}px)`,
            duration: 0.3,
            ease: "back.out(1.5)",
          });

          // Dim other segments
          paths.forEach(p => {
            if (p !== path) {
              safeGsap.to(p, { opacity: 0.5, duration: 0.3 });
            }
          });
        });

        path.addEventListener("mousemove", (e: MouseEvent) => {
          tooltip.style.left = `${e.pageX}px`;
          tooltip.style.top = `${e.pageY - 40}px`;
        });

        path.addEventListener("mouseleave", () => {
          tooltip.style.opacity = "0";

          safeGsap.to(path, {
            transform: "translate(0, 0)",
            duration: 0.3,
          });

          // Reset all segments
          paths.forEach(p => {
            safeGsap.to(p, { opacity: 1, duration: 0.3 });
          });
        });

        // Start with opacity 0 for animation
        path.style.opacity = "0";
        path.style.transformOrigin = `${centerX}px ${centerY}px`;
        svg.appendChild(path);
        paths.push(path);

        // Enhanced animation with sequential reveal
        safeGsap.to(path, {
          opacity: 1,
          duration: 0.6,
          delay: index * 0.15,
          ease: "power2.out",
        });

        // Add a subtle rotation animation
        safeGsap.from(path, {
          rotation: -5,
          duration: 0.8,
          delay: index * 0.15,
          ease: "elastic.out(1, 0.5)",
        });

        // Create enhanced legend items
        const legendItem = document.createElement("div");
        legendItem.className = "flex items-center text-sm p-2 rounded hover:bg-muted/50 transition-colors duration-200 cursor-pointer";

        const colorBox = document.createElement("div");
        colorBox.className = "w-4 h-4 mr-2 rounded-sm";
        colorBox.style.backgroundColor = typeColors[typeCount.type] || "#6b7280";

        const label = document.createElement("span");
        label.textContent = `${typeCount.type}: ${typeCount._count.id} (${Math.round(percentage * 100)}%)`;

        // Add legend item interaction
        legendItem.addEventListener("mouseenter", () => {
          const typeSegment = paths.find(p => p.getAttribute("data-type") === typeCount.type);
          if (typeSegment) {
            // Highlight corresponding pie segment
            const translateX = Math.cos(midAngleRad) * 10;
            const translateY = Math.sin(midAngleRad) * 10;

            safeGsap.to(typeSegment, {
              transform: `translate(${translateX}px, ${translateY}px)`,
              opacity: 1,
              duration: 0.3,
            });

            // Dim other segments
            paths.forEach(p => {
              if (p !== typeSegment) {
                safeGsap.to(p, { opacity: 0.5, duration: 0.3 });
              }
            });

            // Highlight legend item
            safeGsap.to(colorBox, {
              scale: 1.2,
              duration: 0.3,
            });
          }
        });

        legendItem.addEventListener("mouseleave", () => {
          // Reset all segments
          paths.forEach(p => {
            safeGsap.to(p, {
              opacity: 1,
              transform: "translate(0, 0)",
              duration: 0.3,
            });
          });

          // Reset legend item
          safeGsap.to(colorBox, {
            scale: 1,
            duration: 0.3,
          });
        });

        legendItem.appendChild(colorBox);
        legendItem.appendChild(label);
        legend.appendChild(legendItem);

        startAngle = endAngle;
      });

      // Animate center circle appearance
      svg.appendChild(centerCircle);
      safeGsap.fromTo(
        centerCircle,
        { opacity: 0, scale: 0.5 },
        { opacity: 0.7, scale: 1, duration: 1, delay: 0.8, ease: "elastic.out(1, 0.5)" }
      );

      chartContainer.appendChild(svg);
      chartContainer.appendChild(legend);
      typesChartRef.current.appendChild(chartContainer);
      typesChartRef.current.appendChild(tooltip);
    }
  }, [serviceOrdersByMonth, topCustomers, topDevices, serviceOrderTypes]);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Service Orders by Month</CardTitle>
        </CardHeader>
        <CardContent>
          <div ref={monthlyChartRef} className="h-64"></div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Top Customers</CardTitle>
          </CardHeader>
          <CardContent>
            <div ref={customersChartRef} className="h-64 overflow-y-auto"></div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Top Devices</CardTitle>
          </CardHeader>
          <CardContent>
            <div ref={devicesChartRef} className="h-64 overflow-y-auto"></div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Service Order Types</CardTitle>
        </CardHeader>
        <CardContent>
          <div ref={typesChartRef} className="h-64 flex flex-col items-center justify-center"></div>
        </CardContent>
      </Card>
    </div>
  );
}