import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON>s, CartesianGrid, <PERSON>lt<PERSON>, Legend, ResponsiveContainer } from '~/components/ui/chart';

interface TelemetryDataPoint {
  timestamp: string | Date;
  temperature: number | null;
  pressure: number | null;
  vibration: number | null;
  humidity: number | null;
  noise: number | null;
  powerUsage: number | null;
  runtime: number | null;
  cycles: number | null;
  [key: string]: any; // For any additional properties
}

interface TelemetryChartProps {
  telemetry: TelemetryDataPoint[];
  title: string;
  description: string;
}

export function TelemetryChart({ telemetry, title, description }: TelemetryChartProps) {
  if (telemetry.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg">
        <p className="text-gray-500">No telemetry data available for chart</p>
      </div>
    );
  }

  // Prepare data for the chart - limit to last 100 points for better performance
  const chartData = [...telemetry]
    .filter(t => t.timestamp) // Filter out entries without timestamp
    .sort((a, b) => {
      const timeA = new Date(a.timestamp).getTime();
      const timeB = new Date(b.timestamp).getTime();
      return timeA - timeB;
    })
    .slice(-100);

  // Find available metrics to display (only include metrics with non-null values)
  const availableMetrics = ['temperature', 'pressure', 'vibration', 'humidity', 'powerUsage']
    .filter(metric => chartData.some(d => {
      const value = d[metric as keyof TelemetryDataPoint];
      return value !== null && value !== undefined && !isNaN(Number(value));
    }));

  const colors = {
    temperature: '#8884d8',
    pressure: '#82ca9d',
    vibration: '#ffc658',
    humidity: '#0088FE',
    powerUsage: '#FF8042',
  };

  return (
    <div className="bg-white p-4 rounded-lg shadow">
      <h3 className="text-lg font-medium mb-2">{title}</h3>
      <p className="text-sm text-gray-500 mb-4">{description}</p>
      
      <div className="h-80 w-full">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={chartData}
            margin={{
              top: 5,
              right: 30,
              left: 20,
              bottom: 5,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey="timestamp" 
              tickFormatter={(value) => {
                try {
                  return new Date(value).toLocaleTimeString();
                } catch (e) {
                  return '';
                }
              }}
            />
            <YAxis yAxisId="left" />
            <YAxis yAxisId="right" orientation="right" />
            <Tooltip 
              labelFormatter={(value) => {
                try {
                  return new Date(value).toLocaleString();
                } catch (e) {
                  return '';
                }
              }}
            />
            <Legend />
            
            {availableMetrics.map((metric) => (
              <Line
                key={metric}
                yAxisId={['temperature', 'pressure', 'humidity'].includes(metric) ? 'left' : 'right'}
                type="monotone"
                dataKey={metric}
                stroke={colors[metric as keyof typeof colors]}
                activeDot={{ r: 4 }}
                name={metric.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
              />
            ))}
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}
