// 📈 Forecasting Dashboard - AI-Powered Sales Forecasting Visualization
// Claude 4 w Augment Framework - COSMIC-LEVEL ANALYTICS! ✨

import { useState, useEffect } from "react";
import { useFetcher } from "@remix-run/react";
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ReferenceLine
} from "~/components/ui/chart";
import {
  TrendingUp,
  TrendingDown,
  Brain,
  Target,
  Calendar,
  DollarSign,
  BarChart3,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  Activity,
  Zap,
  AlertTriangle,
  CheckCircle,
  Clock,
  Filter,
  Download,
  RefreshCw,
  Settings
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "~/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Progress } from "~/components/ui/progress";
import type { ForecastEntry, ForecastAnalytics } from "~/models/forecasting";

interface ForecastingDashboardProps {
  forecasts: ForecastEntry[];
  analytics?: ForecastAnalytics;
  className?: string;
}

interface ChartDataPoint {
  period: string;
  predicted: number;
  actual?: number;
  lowerBound: number;
  upperBound: number;
  confidence: number;
}

export function ForecastingDashboard({ 
  forecasts, 
  analytics, 
  className = "" 
}: ForecastingDashboardProps) {
  const [selectedPeriod, setSelectedPeriod] = useState<string>("monthly");
  const [selectedModel, setSelectedModel] = useState<string>("all");
  const [chartData, setChartData] = useState<ChartDataPoint[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const fetcher = useFetcher();

  useEffect(() => {
    prepareChartData();
  }, [forecasts, selectedPeriod]);

  const prepareChartData = () => {
    const filteredForecasts = forecasts.filter(f => 
      selectedPeriod === "all" || f.periodType === selectedPeriod
    );

    const data = filteredForecasts.map(forecast => ({
      period: formatPeriod(forecast.periodStart, forecast.periodEnd),
      predicted: forecast.predictedRevenue,
      actual: forecast.actualRevenue,
      lowerBound: forecast.lowerBound,
      upperBound: forecast.upperBound,
      confidence: forecast.confidence
    }));

    setChartData(data.sort((a, b) => a.period.localeCompare(b.period)));
  };

  const formatPeriod = (start: Date, end: Date) => {
    return start.toLocaleDateString('pl-PL', { 
      year: 'numeric', 
      month: 'short' 
    });
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pl-PL', {
      style: 'currency',
      currency: 'PLN',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  const calculateAccuracy = () => {
    const completedForecasts = forecasts.filter(f => f.actualRevenue);
    if (completedForecasts.length === 0) return 0;

    const totalAccuracy = completedForecasts.reduce((sum, f) => {
      const accuracy = 100 - Math.abs((f.actualRevenue! - f.predictedRevenue) / f.predictedRevenue * 100);
      return sum + Math.max(0, accuracy);
    }, 0);

    return totalAccuracy / completedForecasts.length;
  };

  const getLatestForecast = () => {
    return forecasts.sort((a, b) => 
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    )[0];
  };

  const getTrendDirection = () => {
    if (chartData.length < 2) return 'stable';
    const recent = chartData.slice(-2);
    return recent[1].predicted > recent[0].predicted ? 'up' : 
           recent[1].predicted < recent[0].predicted ? 'down' : 'stable';
  };

  const refreshForecasts = () => {
    setIsLoading(true);
    fetcher.submit(
      { action: "refresh_forecasts" },
      { method: "post" }
    );
    setTimeout(() => setIsLoading(false), 2000);
  };

  const latestForecast = getLatestForecast();
  const accuracy = calculateAccuracy();
  const trendDirection = getTrendDirection();

  const COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-3 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-lg">
            <Brain className="h-6 w-6 text-blue-400" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-white">Sales Forecasting</h2>
            <p className="text-slate-400">AI-powered revenue predictions and analytics</p>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-40 bg-slate-800 border-slate-700 text-white">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-slate-800 border-slate-700">
              <SelectItem value="all">All Periods</SelectItem>
              <SelectItem value="weekly">Weekly</SelectItem>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="quarterly">Quarterly</SelectItem>
            </SelectContent>
          </Select>
          
          <Button 
            variant="outline" 
            onClick={refreshForecasts}
            disabled={isLoading}
            className="border-slate-700 text-slate-300"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          
          <Button variant="outline" className="border-slate-700 text-slate-300">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-slate-800/50 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Target className="h-5 w-5 text-blue-400" />
              <div>
                <div className="text-sm text-slate-400">Next Period Forecast</div>
                <div className="text-2xl font-bold text-white">
                  {latestForecast ? formatCurrency(latestForecast.predictedRevenue) : 'N/A'}
                </div>
                <div className="flex items-center gap-1 text-xs">
                  {trendDirection === 'up' && <TrendingUp className="h-3 w-3 text-green-400" />}
                  {trendDirection === 'down' && <TrendingDown className="h-3 w-3 text-red-400" />}
                  <span className={`${
                    trendDirection === 'up' ? 'text-green-400' : 
                    trendDirection === 'down' ? 'text-red-400' : 'text-slate-400'
                  }`}>
                    {latestForecast?.confidence || 0}% confidence
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800/50 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <CheckCircle className="h-5 w-5 text-green-400" />
              <div>
                <div className="text-sm text-slate-400">Model Accuracy</div>
                <div className="text-2xl font-bold text-green-400">
                  {accuracy.toFixed(1)}%
                </div>
                <Progress value={accuracy} className="h-1 mt-1" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800/50 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <BarChart3 className="h-5 w-5 text-purple-400" />
              <div>
                <div className="text-sm text-slate-400">Total Forecasts</div>
                <div className="text-2xl font-bold text-white">{forecasts.length}</div>
                <div className="text-xs text-slate-400">
                  {forecasts.filter(f => f.status === 'active').length} active
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800/50 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Zap className="h-5 w-5 text-yellow-400" />
              <div>
                <div className="text-sm text-slate-400">AI Model</div>
                <div className="text-lg font-bold text-white">
                  {latestForecast?.modelUsed || 'N/A'}
                </div>
                <Badge variant="outline" className="text-xs border-yellow-500 text-yellow-400">
                  {latestForecast?.modelVersion || 'v1.0.0'}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <Tabs defaultValue="forecast" className="space-y-4">
        <TabsList className="bg-slate-800 border-slate-700">
          <TabsTrigger value="forecast" className="data-[state=active]:bg-slate-700">
            Forecast Trend
          </TabsTrigger>
          <TabsTrigger value="accuracy" className="data-[state=active]:bg-slate-700">
            Accuracy Analysis
          </TabsTrigger>
          <TabsTrigger value="breakdown" className="data-[state=active]:bg-slate-700">
            Service Breakdown
          </TabsTrigger>
          <TabsTrigger value="scenarios" className="data-[state=active]:bg-slate-700">
            Scenarios
          </TabsTrigger>
        </TabsList>

        <TabsContent value="forecast" className="space-y-4">
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Revenue Forecast Trend
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <AreaChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                  <XAxis 
                    dataKey="period" 
                    stroke="#9ca3af"
                    fontSize={12}
                  />
                  <YAxis 
                    stroke="#9ca3af"
                    fontSize={12}
                    tickFormatter={(value) => `${(value / 1000).toFixed(0)}k`}
                  />
                  <Tooltip 
                    contentStyle={{ 
                      backgroundColor: '#1f2937', 
                      border: '1px solid #374151',
                      borderRadius: '8px',
                      color: '#fff'
                    }}
                    formatter={(value: number) => [formatCurrency(value), '']}
                  />
                  <Legend />
                  
                  {/* Confidence Interval */}
                  <Area
                    dataKey="upperBound"
                    stackId="1"
                    stroke="none"
                    fill="#3b82f6"
                    fillOpacity={0.1}
                    name="Upper Bound"
                  />
                  <Area
                    dataKey="lowerBound"
                    stackId="1"
                    stroke="none"
                    fill="#ffffff"
                    fillOpacity={1}
                    name="Lower Bound"
                  />
                  
                  {/* Predicted Revenue */}
                  <Line
                    type="monotone"
                    dataKey="predicted"
                    stroke="#3b82f6"
                    strokeWidth={3}
                    dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
                    name="Predicted Revenue"
                  />
                  
                  {/* Actual Revenue */}
                  <Line
                    type="monotone"
                    dataKey="actual"
                    stroke="#10b981"
                    strokeWidth={2}
                    dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
                    strokeDasharray="5 5"
                    name="Actual Revenue"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="accuracy" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Forecast vs Actual
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={chartData.filter(d => d.actual)}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                    <XAxis dataKey="period" stroke="#9ca3af" fontSize={12} />
                    <YAxis 
                      stroke="#9ca3af" 
                      fontSize={12}
                      tickFormatter={(value) => `${(value / 1000).toFixed(0)}k`}
                    />
                    <Tooltip 
                      contentStyle={{ 
                        backgroundColor: '#1f2937', 
                        border: '1px solid #374151',
                        borderRadius: '8px',
                        color: '#fff'
                      }}
                      formatter={(value: number) => [formatCurrency(value), '']}
                    />
                    <Legend />
                    <Bar dataKey="predicted" fill="#3b82f6" name="Predicted" />
                    <Bar dataKey="actual" fill="#10b981" name="Actual" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Confidence Levels
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                    <XAxis dataKey="period" stroke="#9ca3af" fontSize={12} />
                    <YAxis 
                      stroke="#9ca3af" 
                      fontSize={12}
                      domain={[0, 100]}
                    />
                    <Tooltip 
                      contentStyle={{ 
                        backgroundColor: '#1f2937', 
                        border: '1px solid #374151',
                        borderRadius: '8px',
                        color: '#fff'
                      }}
                      formatter={(value: number) => [`${value}%`, 'Confidence']}
                    />
                    <ReferenceLine y={80} stroke="#f59e0b" strokeDasharray="5 5" />
                    <Line
                      type="monotone"
                      dataKey="confidence"
                      stroke="#8b5cf6"
                      strokeWidth={2}
                      dot={{ fill: '#8b5cf6', strokeWidth: 2, r: 4 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="breakdown" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {latestForecast?.forecastByService && (
              <Card className="bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <PieChartIcon className="h-5 w-5" />
                    Service Breakdown
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={latestForecast.forecastByService.map((service, index) => ({
                          name: service.serviceType,
                          value: service.predictedRevenue,
                          fill: COLORS[index % COLORS.length]
                        }))}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {latestForecast.forecastByService.map((_, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip 
                        contentStyle={{ 
                          backgroundColor: '#1f2937', 
                          border: '1px solid #374151',
                          borderRadius: '8px',
                          color: '#fff'
                        }}
                        formatter={(value: number) => [formatCurrency(value), 'Revenue']}
                      />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            )}

            {latestForecast?.forecastBySource && (
              <Card className="bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <BarChart3 className="h-5 w-5" />
                    Source Performance
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={latestForecast.forecastBySource}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                      <XAxis 
                        dataKey="source" 
                        stroke="#9ca3af" 
                        fontSize={12}
                        angle={-45}
                        textAnchor="end"
                        height={80}
                      />
                      <YAxis 
                        stroke="#9ca3af" 
                        fontSize={12}
                        tickFormatter={(value) => `${(value / 1000).toFixed(0)}k`}
                      />
                      <Tooltip 
                        contentStyle={{ 
                          backgroundColor: '#1f2937', 
                          border: '1px solid #374151',
                          borderRadius: '8px',
                          color: '#fff'
                        }}
                        formatter={(value: number) => [formatCurrency(value), 'Revenue']}
                      />
                      <Bar dataKey="predictedRevenue" fill="#10b981" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="scenarios" className="space-y-4">
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Scenario Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-4 bg-green-500/10 border border-green-500/30 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <TrendingUp className="h-4 w-4 text-green-400" />
                    <span className="font-medium text-green-400">Optimistic</span>
                  </div>
                  <div className="text-2xl font-bold text-white">
                    {latestForecast ? formatCurrency(latestForecast.predictedRevenue * 1.2) : 'N/A'}
                  </div>
                  <div className="text-sm text-green-300">+20% from baseline</div>
                </div>

                <div className="p-4 bg-blue-500/10 border border-blue-500/30 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Target className="h-4 w-4 text-blue-400" />
                    <span className="font-medium text-blue-400">Realistic</span>
                  </div>
                  <div className="text-2xl font-bold text-white">
                    {latestForecast ? formatCurrency(latestForecast.predictedRevenue) : 'N/A'}
                  </div>
                  <div className="text-sm text-blue-300">Baseline forecast</div>
                </div>

                <div className="p-4 bg-red-500/10 border border-red-500/30 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <TrendingDown className="h-4 w-4 text-red-400" />
                    <span className="font-medium text-red-400">Pessimistic</span>
                  </div>
                  <div className="text-2xl font-bold text-white">
                    {latestForecast ? formatCurrency(latestForecast.predictedRevenue * 0.8) : 'N/A'}
                  </div>
                  <div className="text-sm text-red-300">-20% from baseline</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
