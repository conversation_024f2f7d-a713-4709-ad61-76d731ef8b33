/**
 * Note Server Model - GoBackend-Kratos Integration
 * Provides note management functionality through API
 */

import { prisma } from '~/db.server';

export interface Note {
  id: string;
  title?: string;
  content: string;
  type: 'general' | 'service' | 'maintenance' | 'customer' | 'internal' | 'reminder';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  entityType: 'CUSTOMER' | 'DEVICE' | 'SERVICE_ORDER' | 'INVOICE' | 'USER' | 'GENERAL';
  entityId?: string;
  isPrivate: boolean;
  isPinned: boolean;
  tags: string[];
  attachments: string[];
  reminderDate?: Date;
  authorId: string;
  authorName: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface NoteFilter {
  type?: Note['type'];
  priority?: Note['priority'];
  entityType?: Note['entityType'];
  entityId?: string;
  authorId?: string;
  isPrivate?: boolean;
  isPinned?: boolean;
  tags?: string[];
  search?: string;
  dateFrom?: Date;
  dateTo?: Date;
}

/**
 * Create a new note
 */
export async function createNote(
  data: Omit<Note, 'id' | 'createdAt' | 'updatedAt'>
): Promise<Note | null> {
  try {
    console.log('Mock creating note:', data.title || 'Untitled', 'for entity:', data.entityType, data.entityId);
    
    return {
      id: 'mock-note-' + Date.now(),
      ...data,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  } catch (error) {
    console.error('Error creating note:', error);
    return null;
  }
}

/**
 * Get note by ID
 */
export async function getNote(id: string): Promise<Note | null> {
  try {
    console.log('Mock getting note:', id);
    
    return {
      id,
      title: 'Mock Note',
      content: 'This is a mock note content for testing purposes.',
      type: 'general',
      priority: 'MEDIUM',
      entityType: 'CUSTOMER',
      entityId: 'mock-customer-id',
      isPrivate: false,
      isPinned: false,
      tags: ['important', 'follow-up'],
      attachments: [],
      authorId: 'mock-user-id',
      authorName: 'Mock User',
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  } catch (error) {
    console.error('Error getting note:', error);
    return null;
  }
}

/**
 * Get notes with filters
 */
export async function getNotes(
  filters?: NoteFilter,
  options?: {
    limit?: number;
    offset?: number;
    sortBy?: 'createdAt' | 'updatedAt' | 'priority' | 'title';
    sortOrder?: 'asc' | 'desc';
  }
): Promise<{ notes: Note[]; total: number }> {
  try {
    console.log('Mock getting notes with filters:', filters, 'options:', options);
    
    return {
      notes: [],
      total: 0,
    };
  } catch (error) {
    console.error('Error getting notes:', error);
    return { notes: [], total: 0 };
  }
}

/**
 * Get notes for an entity
 */
export async function getNotesForEntity(
  entityType: Note['entityType'],
  entityId: string,
  options?: {
    includePrivate?: boolean;
    limit?: number;
    offset?: number;
  }
): Promise<{ notes: Note[]; total: number }> {
  try {
    console.log('Mock getting notes for entity:', entityType, entityId);
    
    return {
      notes: [],
      total: 0,
    };
  } catch (error) {
    console.error('Error getting notes for entity:', error);
    return { notes: [], total: 0 };
  }
}

/**
 * Update a note
 */
export async function updateNote(
  id: string,
  data: Partial<Omit<Note, 'id' | 'createdAt' | 'updatedAt'>>
): Promise<Note | null> {
  try {
    console.log('Mock updating note:', id, Object.keys(data));
    
    const existingNote = await getNote(id);
    if (!existingNote) return null;
    
    return {
      ...existingNote,
      ...data,
      updatedAt: new Date(),
    };
  } catch (error) {
    console.error('Error updating note:', error);
    return null;
  }
}

/**
 * Delete a note
 */
export async function deleteNote(id: string): Promise<boolean> {
  try {
    console.log('Mock deleting note:', id);
    return true;
  } catch (error) {
    console.error('Error deleting note:', error);
    return false;
  }
}

/**
 * Pin/unpin a note
 */
export async function toggleNotePin(id: string): Promise<{ success: boolean; isPinned?: boolean; error?: string }> {
  try {
    console.log('Mock toggling note pin:', id);
    
    const note = await getNote(id);
    if (!note) {
      return { success: false, error: 'Note not found' };
    }
    
    const isPinned = !note.isPinned;
    await updateNote(id, { isPinned });
    
    return { success: true, isPinned };
  } catch (error) {
    console.error('Error toggling note pin:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Add tags to a note
 */
export async function addNoteTags(id: string, tags: string[]): Promise<{ success: boolean; error?: string }> {
  try {
    console.log('Mock adding tags to note:', id, tags);
    
    const note = await getNote(id);
    if (!note) {
      return { success: false, error: 'Note not found' };
    }
    
    const uniqueTags = Array.from(new Set([...note.tags, ...tags]));
    await updateNote(id, { tags: uniqueTags });
    
    return { success: true };
  } catch (error) {
    console.error('Error adding note tags:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Remove tags from a note
 */
export async function removeNoteTags(id: string, tags: string[]): Promise<{ success: boolean; error?: string }> {
  try {
    console.log('Mock removing tags from note:', id, tags);
    
    const note = await getNote(id);
    if (!note) {
      return { success: false, error: 'Note not found' };
    }
    
    const filteredTags = note.tags.filter(tag => !tags.includes(tag));
    await updateNote(id, { tags: filteredTags });
    
    return { success: true };
  } catch (error) {
    console.error('Error removing note tags:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Search notes
 */
export async function searchNotes(
  query: string,
  filters?: NoteFilter,
  options?: {
    limit?: number;
    offset?: number;
  }
): Promise<{ notes: Note[]; total: number }> {
  try {
    console.log('Mock searching notes:', query, 'with filters:', filters);
    
    return {
      notes: [],
      total: 0,
    };
  } catch (error) {
    console.error('Error searching notes:', error);
    return { notes: [], total: 0 };
  }
}

/**
 * Get notes with reminders due
 */
export async function getNotesWithReminders(
  dueDate?: Date
): Promise<Note[]> {
  try {
    const targetDate = dueDate || new Date();
    console.log('Mock getting notes with reminders due by:', targetDate);
    
    return [];
  } catch (error) {
    console.error('Error getting notes with reminders:', error);
    return [];
  }
}

/**
 * Get all unique tags
 */
export async function getAllNoteTags(): Promise<string[]> {
  try {
    console.log('Mock getting all note tags');
    
    return ['important', 'follow-up', 'urgent', 'maintenance', 'customer-request', 'internal'];
  } catch (error) {
    console.error('Error getting all note tags:', error);
    return [];
  }
}

/**
 * Get note statistics
 */
export async function getNoteStatistics(
  entityType?: Note['entityType'],
  entityId?: string
): Promise<{
  total: number;
  byType: Record<string, number>;
  byPriority: Record<string, number>;
  pinned: number;
  withReminders: number;
}> {
  try {
    console.log('Mock getting note statistics for:', entityType, entityId);
    
    return {
      total: 25,
      byType: {
        general: 10,
        service: 8,
        maintenance: 4,
        customer: 2,
        internal: 1,
      },
      byPriority: {
        LOW: 8,
        MEDIUM: 12,
        HIGH: 4,
        URGENT: 1,
      },
      pinned: 3,
      withReminders: 5,
    };
  } catch (error) {
    console.error('Error getting note statistics:', error);
    return {
      total: 0,
      byType: {},
      byPriority: {},
      pinned: 0,
      withReminders: 0,
    };
  }
}

/**
 * Bulk delete notes
 */
export async function bulkDeleteNotes(noteIds: string[]): Promise<{ success: number; failed: number }> {
  try {
    console.log('Mock bulk deleting notes:', noteIds.length);
    
    return {
      success: noteIds.length,
      failed: 0,
    };
  } catch (error) {
    console.error('Error bulk deleting notes:', error);
    return {
      success: 0,
      failed: noteIds.length,
    };
  }
}

/**
 * Bulk update note tags
 */
export async function bulkUpdateNoteTags(
  noteIds: string[],
  tagsToAdd: string[],
  tagsToRemove: string[]
): Promise<{ success: number; failed: number }> {
  try {
    console.log('Mock bulk updating note tags for', noteIds.length, 'notes');
    
    return {
      success: noteIds.length,
      failed: 0,
    };
  } catch (error) {
    console.error('Error bulk updating note tags:', error);
    return {
      success: 0,
      failed: noteIds.length,
    };
  }
}

/**
 * Get note list items (simplified note data for lists)
 */
export async function getNoteListItems(
  filters?: NoteFilter,
  options?: {
    limit?: number;
    offset?: number;
    sortBy?: 'createdAt' | 'updatedAt' | 'priority' | 'title';
    sortOrder?: 'asc' | 'desc';
  }
): Promise<Array<{
  id: string;
  title?: string;
  content: string;
  type: Note['type'];
  priority: Note['priority'];
  isPinned: boolean;
  tags: string[];
  authorName: string;
  createdAt: Date;
  updatedAt: Date;
}>> {
  try {
    console.log('Mock getting note list items with filters:', filters);

    const { notes } = await getNotes(filters, options);

    return notes.map(note => ({
      id: note.id,
      title: note.title,
      content: note.content.substring(0, 200) + (note.content.length > 200 ? '...' : ''),
      type: note.type,
      priority: note.priority,
      isPinned: note.isPinned,
      tags: note.tags,
      authorName: note.authorName,
      createdAt: note.createdAt,
      updatedAt: note.updatedAt,
    }));
  } catch (error) {
    console.error('Error getting note list items:', error);
    return [];
  }
}

// Export placeholder to prevent import errors
export const placeholder = true;
