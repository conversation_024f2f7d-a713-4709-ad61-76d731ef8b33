/**
 * Customer Server Model - GoBackend-Kratos Integration
 * Provides customer management functionality through API
 */

import { prisma } from '~/db.server';

export interface Customer {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
  notes?: string;
  isActive: boolean;
  isPublic?: boolean;
  createdAt: Date;
  updatedAt: Date;
  // Additional HVAC-specific fields
  preferredTechnician?: string;
  serviceHistory?: any[];
  equipmentList?: any[];
  contractType?: string;
  priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
}

/**
 * Get a customer by ID
 */
export async function getCustomer(id: string): Promise<Customer | null> {
  try {
    // For now, return mock data until GoBackend-Kratos API is integrated
    console.log(`Getting customer: ${id}`);
    return {
      id,
      name: 'Mock Customer',
      email: '<EMAIL>',
      phone: '******-0123',
      address: '123 Main St',
      city: 'Warsaw',
      state: 'Mazowieckie',
      zipCode: '00-001',
      country: 'Poland',
      notes: 'Mock customer for testing',
      isActive: true,
      isPublic: false,
      createdAt: new Date(),
      updatedAt: new Date(),
      preferredTechnician: 'tech-1',
      serviceHistory: [],
      equipmentList: [],
      contractType: 'STANDARD',
      priority: 'MEDIUM',
    };
  } catch (error) {
    console.error('Error getting customer:', error);
    return null;
  }
}

/**
 * Get all customers
 */
export async function getCustomers(options?: {
  limit?: number;
  offset?: number;
  search?: string;
  isActive?: boolean;
}): Promise<{ customers: Customer[]; total: number }> {
  try {
    // For now, return mock data until GoBackend-Kratos API is integrated
    console.log('Getting customers with options:', options);
    return {
      customers: [],
      total: 0,
    };
  } catch (error) {
    console.error('Error getting customers:', error);
    return { customers: [], total: 0 };
  }
}

/**
 * Create a new customer
 */
export async function createCustomer(
  data: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>
): Promise<Customer | null> {
  try {
    // For now, return mock data until GoBackend-Kratos API is integrated
    console.log('Creating customer:', data);
    return {
      id: 'mock-customer-id',
      ...data,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  } catch (error) {
    console.error('Error creating customer:', error);
    return null;
  }
}

/**
 * Update a customer
 */
export async function updateCustomer(
  id: string,
  data: Partial<Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>>
): Promise<Customer | null> {
  try {
    // For now, return mock data until GoBackend-Kratos API is integrated
    console.log('Updating customer:', id, data);
    const existingCustomer = await getCustomer(id);
    if (!existingCustomer) return null;
    
    return {
      ...existingCustomer,
      ...data,
      updatedAt: new Date(),
    };
  } catch (error) {
    console.error('Error updating customer:', error);
    return null;
  }
}

/**
 * Delete a customer
 */
export async function deleteCustomer(id: string): Promise<boolean> {
  try {
    // For now, just log the operation until GoBackend-Kratos API is integrated
    console.log('Deleting customer:', id);
    return true;
  } catch (error) {
    console.error('Error deleting customer:', error);
    return false;
  }
}

/**
 * Search customers
 */
export async function searchCustomers(
  query: string,
  options?: { limit?: number; offset?: number }
): Promise<{ customers: Customer[]; total: number }> {
  try {
    // For now, return empty results until GoBackend-Kratos API is integrated
    console.log('Searching customers:', query, options);
    return {
      customers: [],
      total: 0,
    };
  } catch (error) {
    console.error('Error searching customers:', error);
    return { customers: [], total: 0 };
  }
}

/**
 * Get customer by email
 */
export async function getCustomerByEmail(email: string): Promise<Customer | null> {
  try {
    // For now, return null until GoBackend-Kratos API is integrated
    console.log('Getting customer by email:', email);
    return null;
  } catch (error) {
    console.error('Error getting customer by email:', error);
    return null;
  }
}

/**
 * Get customers count
 */
export async function getCustomersCount(): Promise<number> {
  try {
    // For now, return 0 until GoBackend-Kratos API is integrated
    console.log('Getting customers count');
    return 0;
  } catch (error) {
    console.error('Error getting customers count:', error);
    return 0;
  }
}

/**
 * Get recent customers
 */
export async function getRecentCustomers(limit: number = 10): Promise<Customer[]> {
  try {
    // For now, return empty array until GoBackend-Kratos API is integrated
    console.log('Getting recent customers, limit:', limit);
    return [];
  } catch (error) {
    console.error('Error getting recent customers:', error);
    return [];
  }
}
