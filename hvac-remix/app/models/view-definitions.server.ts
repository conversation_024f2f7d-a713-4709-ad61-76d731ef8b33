/**
 * View Definitions Server Model - GoBackend-Kratos Integration
 * Provides view definition management functionality through API
 */

import { prisma } from '~/db.server';

export interface ViewDefinition {
  id: string;
  name: string;
  entityType: string;
  definition: string; // JSON string containing view configuration
  isDefault: boolean;
  isPublic: boolean;
  isSystem: boolean;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ViewColumn {
  field: string;
  label: string;
  type: 'text' | 'number' | 'date' | 'boolean' | 'select' | 'custom';
  width?: number;
  sortable?: boolean;
  filterable?: boolean;
  visible?: boolean;
  order?: number;
}

export interface ViewFilter {
  field: string;
  operator: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'gt' | 'lt' | 'gte' | 'lte' | 'in' | 'notIn';
  value: any;
  type: 'text' | 'number' | 'date' | 'boolean' | 'select';
}

export interface ViewSort {
  field: string;
  direction: 'asc' | 'desc';
}

export interface ViewConfig {
  columns: ViewColumn[];
  filters: ViewFilter[];
  sorting: ViewSort[];
  pagination: {
    pageSize: number;
    showPagination: boolean;
  };
  grouping?: {
    field: string;
    expanded: boolean;
  };
  layout: 'table' | 'grid' | 'list' | 'kanban';
}

/**
 * Get view definition by ID
 */
export async function getViewDefinition(id: string): Promise<ViewDefinition | null> {
  try {
    console.log('Mock getting view definition:', id);
    return {
      id,
      name: 'Mock View',
      entityType: 'CUSTOMER',
      definition: JSON.stringify({
        columns: [
          { field: 'name', label: 'Name', type: 'text', visible: true, order: 1 },
          { field: 'email', label: 'Email', type: 'text', visible: true, order: 2 },
          { field: 'phone', label: 'Phone', type: 'text', visible: true, order: 3 },
        ],
        filters: [],
        sorting: [{ field: 'name', direction: 'asc' }],
        pagination: { pageSize: 25, showPagination: true },
        layout: 'table',
      }),
      isDefault: false,
      isPublic: false,
      isSystem: false,
      createdBy: 'mock-user-id',
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  } catch (error) {
    console.error('Error getting view definition:', error);
    return null;
  }
}

/**
 * Get view definitions for an entity type
 */
export async function getViewDefinitions(
  entityType: string,
  userId?: string
): Promise<ViewDefinition[]> {
  try {
    console.log('Mock getting view definitions for entity type:', entityType, 'user:', userId);
    return [];
  } catch (error) {
    console.error('Error getting view definitions:', error);
    return [];
  }
}

/**
 * Create a new view definition
 */
export async function createViewDefinition(
  data: Omit<ViewDefinition, 'id' | 'createdAt' | 'updatedAt'>
): Promise<ViewDefinition | null> {
  try {
    console.log('Mock creating view definition:', data.name);
    return {
      id: 'mock-view-' + Date.now(),
      ...data,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  } catch (error) {
    console.error('Error creating view definition:', error);
    return null;
  }
}

/**
 * Update a view definition
 */
export async function updateViewDefinition(
  id: string,
  data: Partial<Omit<ViewDefinition, 'id' | 'createdAt' | 'updatedAt'>>
): Promise<ViewDefinition | null> {
  try {
    console.log('Mock updating view definition:', id, Object.keys(data));
    const existingView = await getViewDefinition(id);
    if (!existingView) return null;
    
    return {
      ...existingView,
      ...data,
      updatedAt: new Date(),
    };
  } catch (error) {
    console.error('Error updating view definition:', error);
    return null;
  }
}

/**
 * Delete a view definition
 */
export async function deleteViewDefinition(id: string): Promise<boolean> {
  try {
    console.log('Mock deleting view definition:', id);
    return true;
  } catch (error) {
    console.error('Error deleting view definition:', error);
    return false;
  }
}

/**
 * Get default view for entity type
 */
export async function getDefaultView(entityType: string): Promise<ViewDefinition | null> {
  try {
    console.log('Mock getting default view for entity type:', entityType);
    return {
      id: 'default-' + entityType.toLowerCase(),
      name: `Default ${entityType} View`,
      entityType,
      definition: JSON.stringify({
        columns: [
          { field: 'id', label: 'ID', type: 'text', visible: false, order: 0 },
          { field: 'name', label: 'Name', type: 'text', visible: true, order: 1 },
          { field: 'createdAt', label: 'Created', type: 'date', visible: true, order: 2 },
        ],
        filters: [],
        sorting: [{ field: 'createdAt', direction: 'desc' }],
        pagination: { pageSize: 25, showPagination: true },
        layout: 'table',
      }),
      isDefault: true,
      isPublic: true,
      isSystem: true,
      createdBy: 'system',
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  } catch (error) {
    console.error('Error getting default view:', error);
    return null;
  }
}

/**
 * Set view as default
 */
export async function setDefaultView(
  viewId: string,
  entityType: string,
  userId: string
): Promise<boolean> {
  try {
    console.log('Mock setting default view:', viewId, 'for entity:', entityType, 'user:', userId);
    return true;
  } catch (error) {
    console.error('Error setting default view:', error);
    return false;
  }
}

/**
 * Clone a view definition
 */
export async function cloneViewDefinition(
  sourceViewId: string,
  newName: string,
  userId: string
): Promise<ViewDefinition | null> {
  try {
    console.log('Mock cloning view definition:', sourceViewId, 'as:', newName);
    const sourceView = await getViewDefinition(sourceViewId);
    if (!sourceView) return null;
    
    return await createViewDefinition({
      name: newName,
      entityType: sourceView.entityType,
      definition: sourceView.definition,
      isDefault: false,
      isPublic: false,
      isSystem: false,
      createdBy: userId,
    });
  } catch (error) {
    console.error('Error cloning view definition:', error);
    return null;
  }
}

/**
 * Get available fields for entity type
 */
export async function getAvailableFields(entityType: string): Promise<ViewColumn[]> {
  try {
    console.log('Mock getting available fields for entity type:', entityType);
    
    // Return mock fields based on entity type
    const commonFields: ViewColumn[] = [
      { field: 'id', label: 'ID', type: 'text', sortable: true, filterable: true },
      { field: 'createdAt', label: 'Created', type: 'date', sortable: true, filterable: true },
      { field: 'updatedAt', label: 'Updated', type: 'date', sortable: true, filterable: true },
    ];
    
    switch (entityType) {
      case 'CUSTOMER':
        return [
          { field: 'name', label: 'Name', type: 'text', sortable: true, filterable: true },
          { field: 'email', label: 'Email', type: 'text', sortable: true, filterable: true },
          { field: 'phone', label: 'Phone', type: 'text', sortable: true, filterable: true },
          { field: 'address', label: 'Address', type: 'text', sortable: false, filterable: true },
          ...commonFields,
        ];
      case 'DEVICE':
        return [
          { field: 'name', label: 'Name', type: 'text', sortable: true, filterable: true },
          { field: 'model', label: 'Model', type: 'text', sortable: true, filterable: true },
          { field: 'manufacturer', label: 'Manufacturer', type: 'text', sortable: true, filterable: true },
          { field: 'serialNumber', label: 'Serial Number', type: 'text', sortable: true, filterable: true },
          ...commonFields,
        ];
      default:
        return commonFields;
    }
  } catch (error) {
    console.error('Error getting available fields:', error);
    return [];
  }
}

/**
 * Validate view definition
 */
export function validateViewDefinition(definition: string): { valid: boolean; errors: string[] } {
  try {
    const config = JSON.parse(definition) as ViewConfig;
    const errors: string[] = [];
    
    // Basic validation
    if (!config.columns || !Array.isArray(config.columns)) {
      errors.push('Columns must be an array');
    }
    
    if (!config.pagination || typeof config.pagination.pageSize !== 'number') {
      errors.push('Pagination pageSize must be a number');
    }
    
    if (!['table', 'grid', 'list', 'kanban'].includes(config.layout)) {
      errors.push('Layout must be one of: table, grid, list, kanban');
    }
    
    return {
      valid: errors.length === 0,
      errors,
    };
  } catch (error) {
    return {
      valid: false,
      errors: ['Invalid JSON format'],
    };
  }
}

/**
 * Get view definitions for entity (alias for getViewDefinitions)
 */
export async function getViewDefinitionsForEntity(
  entityType: string,
  userId?: string
): Promise<ViewDefinition[]> {
  return await getViewDefinitions(entityType, userId);
}

// Export placeholder to prevent import errors
export const placeholder = true;
