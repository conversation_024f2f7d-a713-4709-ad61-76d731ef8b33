/**
 * User Settings Server Model - GoBackend-Kratos Integration
 * Provides user settings management functionality through API
 */

import { prisma } from '~/db.server';

export interface UserSettings {
  id: string;
  userId: string;
  theme: 'light' | 'dark' | 'auto';
  language: string;
  timezone: string;
  dateFormat: string;
  timeFormat: '12h' | '24h';
  currency: string;
  notifications: {
    email: boolean;
    push: boolean;
    sms: boolean;
    inApp: boolean;
    serviceReminders: boolean;
    appointmentReminders: boolean;
    invoiceReminders: boolean;
    systemAlerts: boolean;
  };
  dashboard: {
    layout: 'grid' | 'list';
    widgets: string[];
    refreshInterval: number;
  };
  calendar: {
    defaultView: 'month' | 'week' | 'day';
    workingHours: {
      start: string;
      end: string;
    };
    workingDays: number[];
  };
  privacy: {
    profileVisibility: 'public' | 'private';
    activityTracking: boolean;
    dataSharing: boolean;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface NotificationPreferences {
  email: boolean;
  push: boolean;
  sms: boolean;
  inApp: boolean;
  serviceReminders: boolean;
  appointmentReminders: boolean;
  invoiceReminders: boolean;
  systemAlerts: boolean;
}

export interface DashboardSettings {
  layout: 'grid' | 'list';
  widgets: string[];
  refreshInterval: number;
}

/**
 * Get user settings by user ID
 */
export async function getUserSettings(userId: string): Promise<UserSettings | null> {
  try {
    console.log('Mock getting user settings for user:', userId);
    
    return {
      id: 'mock-settings-' + userId,
      userId,
      theme: 'light',
      language: 'en',
      timezone: 'America/New_York',
      dateFormat: 'MM/DD/YYYY',
      timeFormat: '12h',
      currency: 'USD',
      notifications: {
        email: true,
        push: true,
        sms: false,
        inApp: true,
        serviceReminders: true,
        appointmentReminders: true,
        invoiceReminders: true,
        systemAlerts: true,
      },
      dashboard: {
        layout: 'grid',
        widgets: ['overview', 'recent-customers', 'upcoming-appointments', 'revenue'],
        refreshInterval: 300, // 5 minutes
      },
      calendar: {
        defaultView: 'week',
        workingHours: {
          start: '08:00',
          end: '17:00',
        },
        workingDays: [1, 2, 3, 4, 5], // Monday to Friday
      },
      privacy: {
        profileVisibility: 'private',
        activityTracking: true,
        dataSharing: false,
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  } catch (error) {
    console.error('Error getting user settings:', error);
    return null;
  }
}

/**
 * Create default user settings
 */
export async function createDefaultUserSettings(userId: string): Promise<UserSettings | null> {
  try {
    console.log('Mock creating default user settings for user:', userId);
    
    const defaultSettings: Omit<UserSettings, 'id' | 'createdAt' | 'updatedAt'> = {
      userId,
      theme: 'light',
      language: 'en',
      timezone: 'America/New_York',
      dateFormat: 'MM/DD/YYYY',
      timeFormat: '12h',
      currency: 'USD',
      notifications: {
        email: true,
        push: true,
        sms: false,
        inApp: true,
        serviceReminders: true,
        appointmentReminders: true,
        invoiceReminders: true,
        systemAlerts: true,
      },
      dashboard: {
        layout: 'grid',
        widgets: ['overview', 'recent-customers', 'upcoming-appointments'],
        refreshInterval: 300,
      },
      calendar: {
        defaultView: 'week',
        workingHours: {
          start: '08:00',
          end: '17:00',
        },
        workingDays: [1, 2, 3, 4, 5],
      },
      privacy: {
        profileVisibility: 'private',
        activityTracking: true,
        dataSharing: false,
      },
    };
    
    return {
      id: 'mock-settings-' + userId,
      ...defaultSettings,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  } catch (error) {
    console.error('Error creating default user settings:', error);
    return null;
  }
}

/**
 * Update user settings
 */
export async function updateUserSettings(
  userId: string,
  updates: Partial<Omit<UserSettings, 'id' | 'userId' | 'createdAt' | 'updatedAt'>>
): Promise<UserSettings | null> {
  try {
    console.log('Mock updating user settings for user:', userId, Object.keys(updates));
    
    const existingSettings = await getUserSettings(userId);
    if (!existingSettings) {
      return await createDefaultUserSettings(userId);
    }
    
    return {
      ...existingSettings,
      ...updates,
      updatedAt: new Date(),
    };
  } catch (error) {
    console.error('Error updating user settings:', error);
    return null;
  }
}

/**
 * Update notification preferences
 */
export async function updateNotificationPreferences(
  userId: string,
  preferences: Partial<NotificationPreferences>
): Promise<{ success: boolean; error?: string }> {
  try {
    console.log('Mock updating notification preferences for user:', userId, preferences);
    
    const settings = await getUserSettings(userId);
    if (!settings) {
      return { success: false, error: 'User settings not found' };
    }
    
    await updateUserSettings(userId, {
      notifications: {
        ...settings.notifications,
        ...preferences,
      },
    });
    
    return { success: true };
  } catch (error) {
    console.error('Error updating notification preferences:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Update dashboard settings
 */
export async function updateDashboardSettings(
  userId: string,
  dashboardSettings: Partial<DashboardSettings>
): Promise<{ success: boolean; error?: string }> {
  try {
    console.log('Mock updating dashboard settings for user:', userId, dashboardSettings);
    
    const settings = await getUserSettings(userId);
    if (!settings) {
      return { success: false, error: 'User settings not found' };
    }
    
    await updateUserSettings(userId, {
      dashboard: {
        ...settings.dashboard,
        ...dashboardSettings,
      },
    });
    
    return { success: true };
  } catch (error) {
    console.error('Error updating dashboard settings:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Get notification preferences
 */
export async function getNotificationPreferences(userId: string): Promise<NotificationPreferences | null> {
  try {
    const settings = await getUserSettings(userId);
    return settings?.notifications || null;
  } catch (error) {
    console.error('Error getting notification preferences:', error);
    return null;
  }
}

/**
 * Get dashboard settings
 */
export async function getDashboardSettings(userId: string): Promise<DashboardSettings | null> {
  try {
    const settings = await getUserSettings(userId);
    return settings?.dashboard || null;
  } catch (error) {
    console.error('Error getting dashboard settings:', error);
    return null;
  }
}

/**
 * Reset user settings to defaults
 */
export async function resetUserSettings(userId: string): Promise<{ success: boolean; error?: string }> {
  try {
    console.log('Mock resetting user settings for user:', userId);
    
    await createDefaultUserSettings(userId);
    
    return { success: true };
  } catch (error) {
    console.error('Error resetting user settings:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Export user settings
 */
export async function exportUserSettings(userId: string): Promise<{ settings?: UserSettings; error?: string }> {
  try {
    console.log('Mock exporting user settings for user:', userId);
    
    const settings = await getUserSettings(userId);
    if (!settings) {
      return { error: 'User settings not found' };
    }
    
    return { settings };
  } catch (error) {
    console.error('Error exporting user settings:', error);
    return {
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Import user settings
 */
export async function importUserSettings(
  userId: string,
  settings: Partial<UserSettings>
): Promise<{ success: boolean; error?: string }> {
  try {
    console.log('Mock importing user settings for user:', userId);
    
    // Remove system fields that shouldn't be imported
    const { id, userId: _, createdAt, updatedAt, ...importableSettings } = settings;
    
    await updateUserSettings(userId, importableSettings);
    
    return { success: true };
  } catch (error) {
    console.error('Error importing user settings:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Get available themes
 */
export function getAvailableThemes(): Array<{ value: string; label: string }> {
  return [
    { value: 'light', label: 'Light' },
    { value: 'dark', label: 'Dark' },
    { value: 'auto', label: 'Auto (System)' },
  ];
}

/**
 * Get available languages
 */
export function getAvailableLanguages(): Array<{ value: string; label: string }> {
  return [
    { value: 'en', label: 'English' },
    { value: 'es', label: 'Spanish' },
    { value: 'fr', label: 'French' },
    { value: 'de', label: 'German' },
    { value: 'pl', label: 'Polish' },
  ];
}

/**
 * Get available timezones
 */
export function getAvailableTimezones(): Array<{ value: string; label: string }> {
  return [
    { value: 'America/New_York', label: 'Eastern Time (ET)' },
    { value: 'America/Chicago', label: 'Central Time (CT)' },
    { value: 'America/Denver', label: 'Mountain Time (MT)' },
    { value: 'America/Los_Angeles', label: 'Pacific Time (PT)' },
    { value: 'Europe/London', label: 'Greenwich Mean Time (GMT)' },
    { value: 'Europe/Paris', label: 'Central European Time (CET)' },
    { value: 'Europe/Warsaw', label: 'Central European Time (Warsaw)' },
  ];
}

/**
 * Upsert user settings (create or update)
 */
export async function upsertUserSettings(
  userId: string,
  settings: Partial<Omit<UserSettings, 'id' | 'userId' | 'createdAt' | 'updatedAt'>>
): Promise<UserSettings | null> {
  try {
    console.log('Mock upserting user settings for user:', userId);

    const existingSettings = await getUserSettings(userId);

    if (existingSettings) {
      return await updateUserSettings(userId, settings);
    } else {
      const defaultSettings = await createDefaultUserSettings(userId);
      if (defaultSettings) {
        return await updateUserSettings(userId, settings);
      }
      return null;
    }
  } catch (error) {
    console.error('Error upserting user settings:', error);
    return null;
  }
}

/**
 * Convert user settings to app settings format
 */
export function convertToAppSettings(userSettings: UserSettings): any {
  try {
    console.log('Mock converting user settings to app settings format');

    return {
      theme: userSettings.theme,
      language: userSettings.language,
      timezone: userSettings.timezone,
      dateFormat: userSettings.dateFormat,
      timeFormat: userSettings.timeFormat,
      currency: userSettings.currency,
      notifications: userSettings.notifications,
      dashboard: userSettings.dashboard,
      calendar: userSettings.calendar,
      privacy: userSettings.privacy,
    };
  } catch (error) {
    console.error('Error converting to app settings:', error);
    return {};
  }
}

// Export placeholder to prevent import errors
export const placeholder = true;
