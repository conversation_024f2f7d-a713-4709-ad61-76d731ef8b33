/**
 * trpc-provider.ts - GoBackend-Kratos Integration
 * Provides trpc provider functionality through API
 */

// Temporary mock implementation until GoBackend-Kratos is fully integrated
console.log('trpc-provider.ts loaded - using mock implementation');

// Export placeholder functions to prevent import errors
export const placeholder = true;

// Add specific exports based on file type

import { createTRPCReact } from '@trpc/react-query';

// Mock tRPC setup
export const trpc = createTRPCReact();

export function TRPCProvider({ children }: { children: React.ReactNode }) {
  return <>{children}</>;
}

