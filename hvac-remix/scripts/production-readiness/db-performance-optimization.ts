// Database performance optimization script - Updated for GoBackend-Kratos API
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

console.log('⚠️  Database performance optimization is now handled by GoBackend-Kratos');
console.log('This script is kept for reference but operations should be performed on the Go backend.');
const supabase = createClient(
  process.env.SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || ''
);

/**
 * Analyzes slow-running queries and recommends indexes
 */
async function analyzeSlowQueries() {
  console.log('Analyzing slow queries...');
  
  try {
    // Query Supabase's pg_stat_statements to find slow queries
    const { data, error } = await supabase.rpc('analyze_slow_queries');
    
    if (error) {
      console.error('Error analyzing slow queries:', error);
      return;
    }
    
    console.log('Slow queries analysis:');
    console.table(data);
    
    // Recommend indexes based on slow queries
    console.log('\nRecommended indexes:');
    for (const query of data) {
      if (query.calls > 10 && query.mean_time > 100) {
        console.log(`- Consider adding an index for query: ${query.query}`);
      }
    }
  } catch (error) {
    console.error('Error analyzing slow queries:', error);
  }
}

/**
 * Creates recommended indexes for frequently queried tables
 */
async function createRecommendedIndexes() {
  console.log('Creating recommended indexes...');
  
  try {
    // Execute raw SQL to create indexes
    // These are based on common access patterns in the HVAC CRM
    
    // Customer search index
    await prisma.$executeRaw`
      CREATE INDEX IF NOT EXISTS idx_customer_search 
      ON "Customer" (name, email, phone);
    `;
    
    // Device lookup index
    await prisma.$executeRaw`
      CREATE INDEX IF NOT EXISTS idx_device_lookup
      ON "Device" (serialNumber, model, manufacturer);
    `;
    
    // Service order status index
    await prisma.$executeRaw`
      CREATE INDEX IF NOT EXISTS idx_service_order_status
      ON "ServiceOrder" (status, scheduledDate);
    `;
    
    // Invoice payment status index
    await prisma.$executeRaw`
      CREATE INDEX IF NOT EXISTS idx_invoice_payment_status
      ON "Invoice" (paymentStatus, dueDate);
    `;
    
    // Calendar entry date range index
    await prisma.$executeRaw`
      CREATE INDEX IF NOT EXISTS idx_calendar_entry_date_range
      ON "CalendarEntry" (startTime, endTime);
    `;
    
    // Inventory part stock level index
    await prisma.$executeRaw`
      CREATE INDEX IF NOT EXISTS idx_inventory_part_stock
      ON "InventoryPart" (currentStock, minimumStock, reorderPoint);
    `;
    
    // Sales opportunity status index
    await prisma.$executeRaw`
      CREATE INDEX IF NOT EXISTS idx_sales_opportunity_status
      ON "SalesOpportunity" (status, expectedCloseDate);
    `;
    
    console.log('Indexes created successfully');
  } catch (error) {
    console.error('Error creating indexes:', error);
  }
}

/**
 * Configures database connection pooling
 */
async function configureConnectionPooling() {
  console.log('Configuring connection pooling...');
  
  try {
    // Get current connection pool settings
    const { data: currentSettings, error: settingsError } = await supabase
      .from('pg_settings')
      .select('name, setting')
      .in('name', ['max_connections', 'superuser_reserved_connections']);
    
    if (settingsError) {
      console.error('Error getting current connection pool settings:', settingsError);
      return;
    }
    
    console.log('Current connection pool settings:');
    console.table(currentSettings);
    
    // Update connection pool settings
    // Note: This requires superuser privileges in Supabase
    const { error: updateError } = await supabase.rpc('update_pg_settings', {
      settings: {
        max_connections: 100,
        superuser_reserved_connections: 3,
        idle_in_transaction_session_timeout: '10000',
        statement_timeout: '30000'
      }
    });
    
    if (updateError) {
      console.error('Error updating connection pool settings:', updateError);
      console.log('Note: Updating PostgreSQL settings requires superuser privileges.');
      console.log('You may need to update these settings through the Supabase dashboard.');
    } else {
      console.log('Connection pool settings updated successfully');
    }
  } catch (error) {
    console.error('Error configuring connection pooling:', error);
  }
}

/**
 * Implements query caching strategies
 */
async function implementQueryCaching() {
  console.log('Implementing query caching strategies...');
  
  try {
    // Create a Redis client configuration file
    const redisConfig = `
import { createClient } from 'redis';
import { env } from '~/env.server';

let redisClient: ReturnType<typeof createClient> | null = null;

export async function getRedisClient() {
  if (!redisClient) {
    redisClient = createClient({
      url: env.REDIS_URL,
      socket: {
        reconnectStrategy: (retries) => Math.min(retries * 50, 1000)
      }
    });
    
    redisClient.on('error', (err) => console.error('Redis Client Error', err));
    await redisClient.connect();
  }
  
  return redisClient;
}

export async function cacheGet<T>(key: string): Promise<T | null> {
  try {
    const client = await getRedisClient();
    const data = await client.get(key);
    return data ? JSON.parse(data) as T : null;
  } catch (error) {
    console.error('Redis cache get error:', error);
    return null;
  }
}

export async function cacheSet<T>(key: string, value: T, expirationInSeconds = 3600): Promise<void> {
  try {
    const client = await getRedisClient();
    await client.set(key, JSON.stringify(value), { EX: expirationInSeconds });
  } catch (error) {
    console.error('Redis cache set error:', error);
  }
}

export async function cacheDelete(key: string): Promise<void> {
  try {
    const client = await getRedisClient();
    await client.del(key);
  } catch (error) {
    console.error('Redis cache delete error:', error);
  }
}

export async function cacheDeletePattern(pattern: string): Promise<void> {
  try {
    const client = await getRedisClient();
    const keys = await client.keys(pattern);
    if (keys.length > 0) {
      await client.del(keys);
    }
  } catch (error) {
    console.error('Redis cache delete pattern error:', error);
  }
}
`;
    
    // Write the Redis configuration file
    const fs = require('fs');
    const path = require('path');
    
    fs.writeFileSync(
      path.join(process.cwd(), 'app', 'utils', 'redis.server.ts'),
      redisConfig
    );
    
    console.log('Redis cache configuration created at app/utils/redis.server.ts');
    
    // Create a sample cached loader
    const cachedLoaderExample = `
import { json } from '@remix-run/node';
import { cacheGet, cacheSet } from '~/utils/redis.server';
import { prisma } from '~/db.server';

export async function getCustomerWithCaching(customerId: string) {
  // Try to get from cache first
  const cacheKey = \`customer:\${customerId}\`;
  const cachedCustomer = await cacheGet(cacheKey);
  
  if (cachedCustomer) {
    return cachedCustomer;
  }
  
  // If not in cache, get from database
  const customer = await prisma.customer.findUnique({
    where: { id: customerId },
    include: {
      devices: true,
      serviceOrders: {
        orderBy: { createdAt: 'desc' },
        take: 5
      }
    }
  });
  
  // Store in cache for 15 minutes
  if (customer) {
    await cacheSet(cacheKey, customer, 900);
  }
  
  return customer;
}

export async function invalidateCustomerCache(customerId: string) {
  const cacheKey = \`customer:\${customerId}\`;
  await cacheDelete(cacheKey);
}
`;
    
    console.log('Sample cached loader example created');
  } catch (error) {
    console.error('Error implementing query caching:', error);
  }
}

/**
 * Main function to run all database optimizations
 */
async function optimizeDatabasePerformance() {
  console.log('Starting database performance optimization...');
  
  await analyzeSlowQueries();
  await createRecommendedIndexes();
  await configureConnectionPooling();
  await implementQueryCaching();
  
  console.log('Database performance optimization completed');
}

// Run the optimization
optimizeDatabasePerformance()
  .catch(console.error)
  .finally(async () => {
    await prisma.$disconnect();
  });