import { type Component, type JSX, createSignal, onMount, onCleanup } from 'solid-js'

// Enhanced Animation System for Full Experience
export interface AnimationConfig {
  duration?: number
  delay?: number
  easing?: string
  transform?: string
  opacity?: number
  scale?: number
  rotate?: number
  translateX?: number
  translateY?: number
  blur?: number
  brightness?: number
  hue?: number
}

export const useCosmicAnimation = () => {
  const [isVisible, setIsVisible] = createSignal(false)
  const [isHovered, setIsHovered] = createSignal(false)
  const [isActive, setIsActive] = createSignal(false)

  const createAnimation = (config: AnimationConfig) => {
    const {
      duration = 300,
      delay = 0,
      easing = 'cubic-bezier(0.4, 0, 0.2, 1)',
      transform = '',
      opacity = 1,
      scale = 1,
      rotate = 0,
      translateX = 0,
      translateY = 0,
      blur = 0,
      brightness = 1,
      hue = 0
    } = config

    return {
      'transition': `all ${duration}ms ${easing} ${delay}ms`,
      'transform': `${transform} scale(${scale}) rotate(${rotate}deg) translateX(${translateX}px) translateY(${translateY}px)`,
      'opacity': opacity,
      'filter': `blur(${blur}px) brightness(${brightness}) hue-rotate(${hue}deg)`
    }
  }

  return {
    isVisible,
    setIsVisible,
    isHovered,
    setIsHovered,
    isActive,
    setIsActive,
    createAnimation
  }
}

// Floating Particles Effect
export const FloatingParticles: Component<{
  count?: number
  color?: string
  size?: number
  speed?: number
}> = (props) => {
  const count = () => props.count || 20
  const color = () => props.color || 'rgba(139, 92, 246, 0.3)'
  const size = () => props.size || 4
  const speed = () => props.speed || 2

  const particles = Array.from({ length: count() }, (_, i) => ({
    id: i,
    x: Math.random() * 100,
    y: Math.random() * 100,
    delay: Math.random() * 5,
    duration: 10 + Math.random() * 10
  }))

  return (
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      {particles.map((particle) => (
        <div
          class="absolute rounded-full opacity-60"
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            width: `${size()}px`,
            height: `${size()}px`,
            'background-color': color(),
            'animation': `float-${particle.id} ${particle.duration}s infinite linear`,
            'animation-delay': `${particle.delay}s`,
            'box-shadow': `0 0 ${size() * 2}px ${color()}`
          }}
        />
      ))}
      <style>{`
        ${particles.map(p => `
          @keyframes float-${p.id} {
            0% { transform: translateY(100vh) rotate(0deg); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateY(-100px) rotate(360deg); opacity: 0; }
          }
        `).join('')}
      `}</style>
    </div>
  )
}

// Magnetic Button Effect
export const MagneticButton: Component<{
  children: JSX.Element
  strength?: number
  class?: string
  onClick?: () => void
}> = (props) => {
  let buttonRef: HTMLButtonElement | undefined
  const [transform, setTransform] = createSignal('')
  const strength = () => props.strength || 0.3

  const handleMouseMove = (e: MouseEvent) => {
    if (!buttonRef) return
    
    const rect = buttonRef.getBoundingClientRect()
    const centerX = rect.left + rect.width / 2
    const centerY = rect.top + rect.height / 2
    
    const deltaX = (e.clientX - centerX) * strength()
    const deltaY = (e.clientY - centerY) * strength()
    
    setTransform(`translate(${deltaX}px, ${deltaY}px) scale(1.05)`)
  }

  const handleMouseLeave = () => {
    setTransform('translate(0px, 0px) scale(1)')
  }

  return (
    <button
      ref={buttonRef}
      class={`transition-all duration-300 ease-out ${props.class || ''}`}
      style={{ transform: transform() }}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      onClick={props.onClick}
    >
      {props.children}
    </button>
  )
}

// Ripple Effect
export const RippleEffect: Component<{
  children: JSX.Element
  color?: string
  duration?: number
  class?: string
  onClick?: () => void
}> = (props) => {
  let containerRef: HTMLDivElement | undefined
  const [ripples, setRipples] = createSignal<Array<{
    id: number
    x: number
    y: number
    size: number
  }>>([])

  const createRipple = (e: MouseEvent) => {
    if (!containerRef) return

    const rect = containerRef.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top
    const size = Math.max(rect.width, rect.height) * 2

    const newRipple = {
      id: Date.now(),
      x: x - size / 2,
      y: y - size / 2,
      size
    }

    setRipples(prev => [...prev, newRipple])

    setTimeout(() => {
      setRipples(prev => prev.filter(r => r.id !== newRipple.id))
    }, props.duration || 600)

    props.onClick?.()
  }

  return (
    <div
      ref={containerRef}
      class={`relative overflow-hidden ${props.class || ''}`}
      onClick={createRipple}
    >
      {props.children}
      {ripples().map(ripple => (
        <div
          class="absolute rounded-full pointer-events-none animate-ping"
          style={{
            left: `${ripple.x}px`,
            top: `${ripple.y}px`,
            width: `${ripple.size}px`,
            height: `${ripple.size}px`,
            'background-color': props.color || 'rgba(255, 255, 255, 0.3)',
            'animation-duration': `${props.duration || 600}ms`
          }}
        />
      ))}
    </div>
  )
}

// Parallax Scroll Effect
export const ParallaxContainer: Component<{
  children: JSX.Element
  speed?: number
  class?: string
}> = (props) => {
  const [offset, setOffset] = createSignal(0)
  const speed = () => props.speed || 0.5

  const handleScroll = () => {
    setOffset(window.pageYOffset * speed())
  }

  onMount(() => {
    window.addEventListener('scroll', handleScroll)
  })

  onCleanup(() => {
    window.removeEventListener('scroll', handleScroll)
  })

  return (
    <div
      class={props.class}
      style={{ transform: `translateY(${offset()}px)` }}
    >
      {props.children}
    </div>
  )
}

// Glow Effect on Hover
export const GlowEffect: Component<{
  children: JSX.Element
  color?: string
  intensity?: number
  class?: string
}> = (props) => {
  const [isHovered, setIsHovered] = createSignal(false)
  const color = () => props.color || '#8b5cf6'
  const intensity = () => props.intensity || 20

  return (
    <div
      class={`transition-all duration-300 ${props.class || ''}`}
      style={{
        'box-shadow': isHovered() 
          ? `0 0 ${intensity()}px ${color()}, 0 0 ${intensity() * 2}px ${color()}40`
          : 'none'
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {props.children}
    </div>
  )
}

// Morphing Shape Background
export const MorphingBackground: Component<{
  colors?: string[]
  duration?: number
  class?: string
}> = (props) => {
  const colors = () => props.colors || [
    '#8b5cf6', '#06b6d4', '#10b981', '#f59e0b', '#ef4444'
  ]
  const duration = () => props.duration || 10

  return (
    <div class={`absolute inset-0 ${props.class || ''}`}>
      <div
        class="w-full h-full opacity-20"
        style={{
          background: `linear-gradient(-45deg, ${colors().join(', ')})`,
          'background-size': '400% 400%',
          animation: `gradient-shift ${duration()}s ease infinite`
        }}
      />
      <style>{`
        @keyframes gradient-shift {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }
      `}</style>
    </div>
  )
}
