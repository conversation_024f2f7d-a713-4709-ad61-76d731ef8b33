import { type Component, createSignal, onMount, onCleanup, Show, For } from 'solid-js'
import { Portal } from 'solid-js/web'
import { CosmicCard } from '../atoms/CosmicCard'
import { GoldenButton } from '../atoms/GoldenButton'
import { R<PERSON>pleEffect, GlowEffect } from '../atoms/CosmicAnimations'
import {
  Bell,
  X,
  Check,
  Info,
  AlertTriangle,
  AlertCircle,
  Zap,
  Users,
  Calendar,
  Mail,
  Phone,
  Settings,
  Star,
  TrendingUp,
  Activity,
  Clock,
  Sparkles
} from 'lucide-solid'

export interface Notification {
  id: string
  type: 'success' | 'info' | 'warning' | 'error' | 'cosmic'
  title: string
  message: string
  timestamp: Date
  read: boolean
  action?: {
    label: string
    onClick: () => void
  }
  category?: string
  priority?: 'low' | 'medium' | 'high' | 'urgent'
  autoClose?: boolean
  duration?: number
}

export interface NotificationSystemProps {
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'
  maxNotifications?: number
  showBadge?: boolean
}

// Global notification state
const [notifications, setNotifications] = createSignal<Notification[]>([])
const [isOpen, setIsOpen] = createSignal(false)

// Notification API
export const notificationAPI = {
  add: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => {
    const newNotification: Notification = {
      ...notification,
      id: Date.now().toString(),
      timestamp: new Date(),
      read: false,
      autoClose: notification.autoClose ?? true,
      duration: notification.duration ?? 5000
    }
    
    setNotifications(prev => [newNotification, ...prev])
    
    // Auto-close if enabled
    if (newNotification.autoClose) {
      setTimeout(() => {
        notificationAPI.remove(newNotification.id)
      }, newNotification.duration)
    }
    
    return newNotification.id
  },
  
  remove: (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id))
  },
  
  markAsRead: (id: string) => {
    setNotifications(prev => prev.map(n => 
      n.id === id ? { ...n, read: true } : n
    ))
  },
  
  markAllAsRead: () => {
    setNotifications(prev => prev.map(n => ({ ...n, read: true })))
  },
  
  clear: () => {
    setNotifications([])
  },
  
  // Convenience methods
  success: (title: string, message: string, options?: Partial<Notification>) => 
    notificationAPI.add({ type: 'success', title, message, ...options }),
    
  info: (title: string, message: string, options?: Partial<Notification>) => 
    notificationAPI.add({ type: 'info', title, message, ...options }),
    
  warning: (title: string, message: string, options?: Partial<Notification>) => 
    notificationAPI.add({ type: 'warning', title, message, ...options }),
    
  error: (title: string, message: string, options?: Partial<Notification>) => 
    notificationAPI.add({ type: 'error', title, message, ...options }),
    
  cosmic: (title: string, message: string, options?: Partial<Notification>) => 
    notificationAPI.add({ type: 'cosmic', title, message, ...options })
}

export const CosmicNotificationSystem: Component<NotificationSystemProps> = (props) => {
  const position = () => props.position || 'top-right'
  const maxNotifications = () => props.maxNotifications || 5
  const showBadge = () => props.showBadge !== false

  const unreadCount = () => notifications().filter(n => !n.read).length
  const visibleNotifications = () => notifications().slice(0, maxNotifications())

  const getPositionClasses = () => {
    switch (position()) {
      case 'top-left': return 'top-4 left-4'
      case 'top-right': return 'top-4 right-4'
      case 'bottom-left': return 'bottom-4 left-4'
      case 'bottom-right': return 'bottom-4 right-4'
    }
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success': return Check
      case 'info': return Info
      case 'warning': return AlertTriangle
      case 'error': return AlertCircle
      case 'cosmic': return Sparkles
      default: return Info
    }
  }

  const getNotificationColors = (type: string) => {
    switch (type) {
      case 'success': return {
        bg: 'from-green-500/20 to-emerald-500/20',
        border: 'border-green-400/30',
        icon: 'text-green-400',
        glow: '#10b981'
      }
      case 'info': return {
        bg: 'from-blue-500/20 to-cyan-500/20',
        border: 'border-blue-400/30',
        icon: 'text-blue-400',
        glow: '#3b82f6'
      }
      case 'warning': return {
        bg: 'from-yellow-500/20 to-orange-500/20',
        border: 'border-yellow-400/30',
        icon: 'text-yellow-400',
        glow: '#f59e0b'
      }
      case 'error': return {
        bg: 'from-red-500/20 to-pink-500/20',
        border: 'border-red-400/30',
        icon: 'text-red-400',
        glow: '#ef4444'
      }
      case 'cosmic': return {
        bg: 'from-cosmic-500/20 to-divine-500/20',
        border: 'border-cosmic-400/30',
        icon: 'text-cosmic-400',
        glow: '#8b5cf6'
      }
      default: return {
        bg: 'from-gray-500/20 to-gray-600/20',
        border: 'border-gray-400/30',
        icon: 'text-gray-400',
        glow: '#6b7280'
      }
    }
  }

  const getPriorityIndicator = (priority?: string) => {
    switch (priority) {
      case 'urgent': return 'animate-pulse ring-2 ring-red-400'
      case 'high': return 'ring-1 ring-orange-400'
      case 'medium': return 'ring-1 ring-yellow-400'
      default: return ''
    }
  }

  return (
    <>
      {/* Notification Bell */}
      <div class={`fixed ${getPositionClasses()} z-40`}>
        <div class="relative">
          <RippleEffect color="rgba(139, 92, 246, 0.3)">
            <GlowEffect color="#8b5cf6" intensity={unreadCount() > 0 ? 20 : 0}>
              <GoldenButton
                variant={unreadCount() > 0 ? 'cosmic' : 'glass'}
                size="md"
                glow={unreadCount() > 0}
                onClick={() => setIsOpen(!isOpen())}
                class="relative"
              >
                <Bell size={20} class={unreadCount() > 0 ? 'animate-pulse' : ''} />
                <Show when={showBadge() && unreadCount() > 0}>
                  <div class="absolute -top-2 -right-2 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center text-xs text-white font-bold animate-bounce">
                    {unreadCount() > 9 ? '9+' : unreadCount()}
                  </div>
                </Show>
              </GoldenButton>
            </GlowEffect>
          </RippleEffect>
        </div>
      </div>

      {/* Notification Panel */}
      <Show when={isOpen()}>
        <Portal>
          <div class={`fixed ${getPositionClasses()} z-50 mt-16`}>
            <CosmicCard variant="cosmic" size="lg" glow class="w-96 max-h-96 overflow-hidden">
              {/* Header */}
              <div class="flex items-center justify-between p-4 border-b border-white/10">
                <div class="flex items-center space-x-2">
                  <Bell size={20} class="text-cosmic-400" />
                  <h3 class="text-lg font-bold text-white">Notifications</h3>
                  <Show when={unreadCount() > 0}>
                    <span class="px-2 py-0.5 bg-cosmic-400/20 text-cosmic-300 text-xs rounded-full">
                      {unreadCount()} new
                    </span>
                  </Show>
                </div>
                
                <div class="flex items-center space-x-1">
                  <Show when={notifications().length > 0}>
                    <GoldenButton
                      variant="glass"
                      size="xs"
                      glow
                      onClick={notificationAPI.markAllAsRead}
                    >
                      <Check size={12} />
                    </GoldenButton>
                  </Show>
                  
                  <GoldenButton
                    variant="glass"
                    size="xs"
                    glow
                    onClick={() => setIsOpen(false)}
                  >
                    <X size={12} />
                  </GoldenButton>
                </div>
              </div>

              {/* Notifications List */}
              <div class="max-h-80 overflow-y-auto">
                <Show when={notifications().length === 0}>
                  <div class="p-8 text-center text-white/70">
                    <Bell size={32} class="mx-auto mb-2 opacity-50" />
                    <div class="text-sm">No notifications</div>
                  </div>
                </Show>

                <For each={visibleNotifications()}>
                  {(notification) => {
                    const colors = getNotificationColors(notification.type)
                    const NotificationIcon = getNotificationIcon(notification.type)
                    
                    return (
                      <div
                        class={`p-4 border-b border-white/5 last:border-b-0 transition-all duration-300 hover:bg-white/5 ${
                          !notification.read ? 'bg-white/5' : ''
                        } ${getPriorityIndicator(notification.priority)}`}
                      >
                        <div class="flex items-start space-x-3">
                          <GlowEffect color={colors.glow} intensity={10}>
                            <div class={`w-8 h-8 bg-gradient-to-r ${colors.bg} rounded-lg border ${colors.border} flex items-center justify-center flex-shrink-0`}>
                              <NotificationIcon size={16} class={colors.icon} />
                            </div>
                          </GlowEffect>
                          
                          <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between">
                              <h4 class="text-white font-medium text-sm truncate">
                                {notification.title}
                              </h4>
                              <div class="flex items-center space-x-1">
                                <Show when={!notification.read}>
                                  <div class="w-2 h-2 bg-cosmic-400 rounded-full" />
                                </Show>
                                <button
                                  class="text-white/50 hover:text-white transition-colors"
                                  onClick={() => notificationAPI.remove(notification.id)}
                                >
                                  <X size={12} />
                                </button>
                              </div>
                            </div>
                            
                            <p class="text-white/70 text-xs mt-1 leading-relaxed">
                              {notification.message}
                            </p>
                            
                            <div class="flex items-center justify-between mt-2">
                              <span class="text-white/50 text-xs">
                                {notification.timestamp.toLocaleTimeString()}
                              </span>
                              
                              <Show when={notification.action}>
                                <GoldenButton
                                  variant="cosmic"
                                  size="xs"
                                  glow
                                  onClick={() => {
                                    notification.action!.onClick()
                                    notificationAPI.markAsRead(notification.id)
                                  }}
                                >
                                  {notification.action!.label}
                                </GoldenButton>
                              </Show>
                            </div>
                          </div>
                        </div>
                      </div>
                    )
                  }}
                </For>
              </div>

              {/* Footer */}
              <Show when={notifications().length > maxNotifications()}>
                <div class="p-3 border-t border-white/10 text-center">
                  <button
                    class="text-cosmic-400 text-xs hover:text-cosmic-300 transition-colors"
                    onClick={() => console.log('View all notifications')}
                  >
                    View all {notifications().length} notifications
                  </button>
                </div>
              </Show>
            </CosmicCard>
          </div>
        </Portal>
      </Show>
    </>
  )
}

// Toast Notifications (floating)
export const CosmicToastContainer: Component<{ position?: string }> = (props) => {
  const [toasts, setToasts] = createSignal<Notification[]>([])
  const position = () => props.position || 'top-right'

  // Listen for new notifications and show as toasts
  const checkForToasts = () => {
    const newNotifications = notifications().filter(n => 
      n.autoClose && !toasts().find(t => t.id === n.id)
    )
    
    if (newNotifications.length > 0) {
      setToasts(prev => [...prev, ...newNotifications])
      
      // Auto-remove toasts
      newNotifications.forEach(notification => {
        setTimeout(() => {
          setToasts(prev => prev.filter(t => t.id !== notification.id))
        }, notification.duration || 5000)
      })
    }
  }

  onMount(() => {
    const interval = setInterval(checkForToasts, 100)
    onCleanup(() => clearInterval(interval))
  })

  const getPositionClasses = () => {
    switch (position()) {
      case 'top-left': return 'top-4 left-4'
      case 'top-right': return 'top-4 right-4'
      case 'bottom-left': return 'bottom-4 left-4'
      case 'bottom-right': return 'bottom-4 right-4'
    }
  }

  return (
    <div class={`fixed ${getPositionClasses()} z-50 space-y-2 pointer-events-none`}>
      <For each={toasts()}>
        {(toast) => {
          const colors = getNotificationColors(toast.type)
          const ToastIcon = getNotificationIcon(toast.type)
          
          return (
            <div class="pointer-events-auto animate-in slide-in-from-right duration-300">
              <GlowEffect color={colors.glow} intensity={15}>
                <CosmicCard variant="glass" size="sm" glow class="w-80">
                  <div class="flex items-start space-x-3 p-3">
                    <div class={`w-6 h-6 bg-gradient-to-r ${colors.bg} rounded border ${colors.border} flex items-center justify-center flex-shrink-0`}>
                      <ToastIcon size={14} class={colors.icon} />
                    </div>
                    
                    <div class="flex-1 min-w-0">
                      <h4 class="text-white font-medium text-sm">{toast.title}</h4>
                      <p class="text-white/70 text-xs mt-1">{toast.message}</p>
                    </div>
                    
                    <button
                      class="text-white/50 hover:text-white transition-colors"
                      onClick={() => setToasts(prev => prev.filter(t => t.id !== toast.id))}
                    >
                      <X size={14} />
                    </button>
                  </div>
                </CosmicCard>
              </GlowEffect>
            </div>
          )
        }}
      </For>
    </div>
  )
}
