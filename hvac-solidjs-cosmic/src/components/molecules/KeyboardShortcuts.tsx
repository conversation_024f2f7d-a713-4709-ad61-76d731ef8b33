import { type Component, createSignal, onMount, onCleanup, Show, For } from 'solid-js'
import { Portal } from 'solid-js/web'
import { CosmicCard } from '../atoms/CosmicCard'
import { GoldenButton } from '../atoms/GoldenButton'
import { 
  Keyboard, 
  Search, 
  Plus, 
  Filter, 
  Download, 
  RefreshCw, 
  Eye, 
  Edit, 
  Trash2,
  ArrowUp,
  ArrowDown,
  ArrowLeft,
  ArrowRight,
  Command,
  X
} from 'lucide-solid'

export interface Shortcut {
  key: string
  description: string
  action: () => void
  category?: string
  modifiers?: ('ctrl' | 'alt' | 'shift' | 'meta')[]
}

export interface KeyboardShortcutsProps {
  shortcuts: Shortcut[]
  showHelp?: boolean
  onToggleHelp?: (show: boolean) => void
}

export const useKeyboardShortcuts = (shortcuts: Shortcut[]) => {
  const [showHelp, setShowHelp] = createSignal(false)
  const [pressedKeys, setPressedKeys] = createSignal<Set<string>>(new Set())

  const normalizeKey = (key: string) => {
    const keyMap: Record<string, string> = {
      ' ': 'Space',
      'Enter': 'Enter',
      'Escape': 'Escape',
      'Tab': 'Tab',
      'Backspace': 'Backspace',
      'Delete': 'Delete',
      'ArrowUp': 'ArrowUp',
      'ArrowDown': 'ArrowDown',
      'ArrowLeft': 'ArrowLeft',
      'ArrowRight': 'ArrowRight'
    }
    return keyMap[key] || key.toUpperCase()
  }

  const getModifierKeys = (event: KeyboardEvent) => {
    const modifiers: string[] = []
    if (event.ctrlKey || event.metaKey) modifiers.push('ctrl')
    if (event.altKey) modifiers.push('alt')
    if (event.shiftKey) modifiers.push('shift')
    return modifiers
  }

  const matchesShortcut = (event: KeyboardEvent, shortcut: Shortcut) => {
    const eventModifiers = getModifierKeys(event)
    const shortcutModifiers = shortcut.modifiers || []
    const eventKey = normalizeKey(event.key)
    const shortcutKey = normalizeKey(shortcut.key)

    // Check if modifiers match
    if (eventModifiers.length !== shortcutModifiers.length) return false
    if (!shortcutModifiers.every(mod => eventModifiers.includes(mod))) return false

    // Check if key matches
    return eventKey === shortcutKey
  }

  const handleKeyDown = (event: KeyboardEvent) => {
    // Update pressed keys for visual feedback
    const key = normalizeKey(event.key)
    setPressedKeys(prev => new Set([...prev, key]))

    // Check for help toggle (? key)
    if (event.key === '?' && !event.ctrlKey && !event.altKey) {
      event.preventDefault()
      setShowHelp(!showHelp())
      return
    }

    // Check for escape to close help
    if (event.key === 'Escape' && showHelp()) {
      event.preventDefault()
      setShowHelp(false)
      return
    }

    // Find matching shortcut
    const matchingShortcut = shortcuts.find(shortcut => matchesShortcut(event, shortcut))
    
    if (matchingShortcut) {
      event.preventDefault()
      matchingShortcut.action()
    }
  }

  const handleKeyUp = (event: KeyboardEvent) => {
    const key = normalizeKey(event.key)
    setPressedKeys(prev => {
      const newSet = new Set(prev)
      newSet.delete(key)
      return newSet
    })
  }

  onMount(() => {
    document.addEventListener('keydown', handleKeyDown)
    document.addEventListener('keyup', handleKeyUp)
  })

  onCleanup(() => {
    document.removeEventListener('keydown', handleKeyDown)
    document.removeEventListener('keyup', handleKeyUp)
  })

  return {
    showHelp,
    setShowHelp,
    pressedKeys
  }
}

export const KeyboardShortcutsHelp: Component<{
  shortcuts: Shortcut[]
  isVisible: boolean
  onClose: () => void
}> = (props) => {
  const categories = () => {
    const cats = new Set(props.shortcuts.map(s => s.category || 'General'))
    return Array.from(cats).sort()
  }

  const getShortcutsByCategory = (category: string) => {
    return props.shortcuts.filter(s => (s.category || 'General') === category)
  }

  const formatShortcut = (shortcut: Shortcut) => {
    const modifiers = shortcut.modifiers || []
    const parts = [...modifiers, shortcut.key]
    return parts.map(part => {
      switch (part) {
        case 'ctrl': return '⌘'
        case 'alt': return '⌥'
        case 'shift': return '⇧'
        case 'meta': return '⌘'
        default: return part.toUpperCase()
      }
    }).join(' + ')
  }

  const getKeyIcon = (key: string) => {
    switch (key.toLowerCase()) {
      case 'arrowup': return <ArrowUp size={12} />
      case 'arrowdown': return <ArrowDown size={12} />
      case 'arrowleft': return <ArrowLeft size={12} />
      case 'arrowright': return <ArrowRight size={12} />
      case 'ctrl':
      case 'meta': return <Command size={12} />
      default: return null
    }
  }

  return (
    <Show when={props.isVisible}>
      <Portal>
        <div class="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* Backdrop */}
          <div 
            class="absolute inset-0 bg-black/50 backdrop-blur-sm"
            onClick={props.onClose}
          />
          
          {/* Modal */}
          <CosmicCard 
            variant="cosmic" 
            size="lg" 
            glow 
            class="relative max-w-2xl w-full max-h-[80vh] overflow-hidden"
          >
            {/* Header */}
            <div class="flex items-center justify-between mb-6 pb-4 border-b border-white/10">
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-gradient-to-r from-cosmic-400 to-divine-400 rounded-lg flex items-center justify-center">
                  <Keyboard size={20} class="text-white" />
                </div>
                <div>
                  <h2 class="text-xl font-bold text-white">Keyboard Shortcuts</h2>
                  <p class="text-white/70 text-sm">Power user commands for faster navigation</p>
                </div>
              </div>
              
              <GoldenButton
                variant="glass"
                size="sm"
                glow
                onClick={props.onClose}
              >
                <X size={16} />
              </GoldenButton>
            </div>

            {/* Content */}
            <div class="overflow-y-auto max-h-96 space-y-6">
              <For each={categories()}>
                {(category) => (
                  <div>
                    <h3 class="text-lg font-semibold text-white mb-3 flex items-center space-x-2">
                      <div class="w-2 h-2 bg-cosmic-400 rounded-full" />
                      <span>{category}</span>
                    </h3>
                    
                    <div class="space-y-2">
                      <For each={getShortcutsByCategory(category)}>
                        {(shortcut) => (
                          <div class="flex items-center justify-between p-3 bg-white/5 rounded-lg border border-white/10 hover:bg-white/10 transition-colors">
                            <div class="flex-1">
                              <div class="text-white font-medium text-sm">
                                {shortcut.description}
                              </div>
                            </div>
                            
                            <div class="flex items-center space-x-1">
                              {(shortcut.modifiers || []).map((modifier) => (
                                <div class="flex items-center justify-center w-6 h-6 bg-white/10 rounded border border-white/20 text-xs text-white/80">
                                  {getKeyIcon(modifier) || modifier.slice(0, 1).toUpperCase()}
                                </div>
                              ))}
                              <Show when={(shortcut.modifiers || []).length > 0}>
                                <span class="text-white/50 text-xs">+</span>
                              </Show>
                              <div class="flex items-center justify-center min-w-6 h-6 bg-cosmic-400/20 rounded border border-cosmic-400/30 text-xs text-cosmic-300 px-2">
                                {getKeyIcon(shortcut.key) || shortcut.key.toUpperCase()}
                              </div>
                            </div>
                          </div>
                        )}
                      </For>
                    </div>
                  </div>
                )}
              </For>
            </div>

            {/* Footer */}
            <div class="mt-6 pt-4 border-t border-white/10">
              <div class="flex items-center justify-between text-sm text-white/70">
                <div class="flex items-center space-x-2">
                  <div class="w-4 h-4 bg-cosmic-400/20 rounded border border-cosmic-400/30 flex items-center justify-center text-xs">
                    ?
                  </div>
                  <span>Press ? to toggle this help</span>
                </div>
                <div class="flex items-center space-x-2">
                  <div class="w-6 h-6 bg-white/10 rounded border border-white/20 flex items-center justify-center text-xs">
                    ESC
                  </div>
                  <span>to close</span>
                </div>
              </div>
            </div>
          </CosmicCard>
        </div>
      </Portal>
    </Show>
  )
}

// Visual indicator for pressed keys
export const KeyboardVisualizer: Component<{
  pressedKeys: Set<string>
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
}> = (props) => {
  const position = () => props.position || 'bottom-right'
  
  const getPositionClasses = () => {
    switch (position()) {
      case 'top-left': return 'top-4 left-4'
      case 'top-right': return 'top-4 right-4'
      case 'bottom-left': return 'bottom-4 left-4'
      case 'bottom-right': return 'bottom-4 right-4'
    }
  }

  return (
    <Show when={props.pressedKeys.size > 0}>
      <div class={`fixed ${getPositionClasses()} z-40 pointer-events-none`}>
        <div class="flex items-center space-x-1 p-2 bg-black/80 backdrop-blur-lg rounded-lg border border-white/20">
          <For each={Array.from(props.pressedKeys)}>
            {(key) => (
              <div class="px-2 py-1 bg-cosmic-400/20 rounded border border-cosmic-400/30 text-cosmic-300 text-xs font-mono min-w-6 text-center">
                {key}
              </div>
            )}
          </For>
        </div>
      </div>
    </Show>
  )
}
