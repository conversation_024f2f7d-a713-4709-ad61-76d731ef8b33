import { type Component, createSignal, createMemo, onMount, onCleanup, Show, For } from 'solid-js'
import { Portal } from 'solid-js/web'
import { CosmicCard } from '../atoms/CosmicCard'
import { 
  Search, 
  Clock, 
  TrendingUp, 
  Star, 
  ArrowRight, 
  X, 
  Filter,
  Sparkles,
  Zap,
  Users,
  Mail,
  Phone,
  MapPin,
  Tag,
  Calendar
} from 'lucide-solid'

export interface SearchResult {
  id: string
  title: string
  subtitle?: string
  description?: string
  category: string
  icon?: any
  score?: number
  metadata?: Record<string, any>
  onClick?: () => void
}

export interface SearchSuggestion {
  text: string
  category: string
  count?: number
}

export interface CosmicSearchProps {
  placeholder?: string
  suggestions?: SearchSuggestion[]
  recentSearches?: string[]
  onSearch: (query: string) => Promise<SearchResult[]> | SearchResult[]
  onSelect?: (result: SearchResult) => void
  showCategories?: boolean
  showRecent?: boolean
  showSuggestions?: boolean
  maxResults?: number
  debounceMs?: number
  minQueryLength?: number
  class?: string
}

export const CosmicSearch: Component<CosmicSearchProps> = (props) => {
  const [query, setQuery] = createSignal('')
  const [isOpen, setIsOpen] = createSignal(false)
  const [results, setResults] = createSignal<SearchResult[]>([])
  const [isLoading, setIsLoading] = createSignal(false)
  const [selectedIndex, setSelectedIndex] = createSignal(-1)
  const [recentSearches, setRecentSearches] = createSignal<string[]>(
    props.recentSearches || JSON.parse(localStorage.getItem('cosmic-search-recent') || '[]')
  )

  let searchRef: HTMLInputElement | undefined
  let resultsRef: HTMLDivElement | undefined
  let debounceTimeout: number | undefined

  const placeholder = () => props.placeholder || 'Search customers, orders, equipment...'
  const maxResults = () => props.maxResults || 10
  const debounceMs = () => props.debounceMs || 300
  const minQueryLength = () => props.minQueryLength || 1

  // Debounced search
  const performSearch = async (searchQuery: string) => {
    if (searchQuery.length < minQueryLength()) {
      setResults([])
      setIsLoading(false)
      return
    }

    setIsLoading(true)
    try {
      const searchResults = await props.onSearch(searchQuery)
      setResults(Array.isArray(searchResults) ? searchResults.slice(0, maxResults()) : [])
    } catch (error) {
      console.error('Search error:', error)
      setResults([])
    } finally {
      setIsLoading(false)
    }
  }

  const debouncedSearch = (searchQuery: string) => {
    if (debounceTimeout) clearTimeout(debounceTimeout)
    debounceTimeout = setTimeout(() => performSearch(searchQuery), debounceMs()) as unknown as number
  }

  // Handle input changes
  const handleInput = (value: string) => {
    setQuery(value)
    setSelectedIndex(-1)
    
    if (value.trim()) {
      setIsOpen(true)
      debouncedSearch(value.trim())
    } else {
      setResults([])
      setIsLoading(false)
      setIsOpen(!!props.showRecent || !!props.showSuggestions)
    }
  }

  // Handle keyboard navigation
  const handleKeyDown = (e: KeyboardEvent) => {
    if (!isOpen()) return

    const totalItems = results().length + (props.showRecent ? recentSearches().length : 0)

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setSelectedIndex(prev => (prev + 1) % totalItems)
        break
      case 'ArrowUp':
        e.preventDefault()
        setSelectedIndex(prev => prev <= 0 ? totalItems - 1 : prev - 1)
        break
      case 'Enter':
        e.preventDefault()
        if (selectedIndex() >= 0) {
          const result = results()[selectedIndex()]
          if (result) {
            handleSelect(result)
          } else {
            // Handle recent search selection
            const recentIndex = selectedIndex() - results().length
            if (recentIndex >= 0 && recentIndex < recentSearches().length) {
              const recentQuery = recentSearches()[recentIndex]
              setQuery(recentQuery)
              debouncedSearch(recentQuery)
            }
          }
        } else if (query().trim()) {
          // Direct search
          addToRecent(query().trim())
          props.onSelect?.({
            id: 'direct-search',
            title: query().trim(),
            category: 'search'
          })
          setIsOpen(false)
        }
        break
      case 'Escape':
        setIsOpen(false)
        searchRef?.blur()
        break
    }
  }

  // Handle result selection
  const handleSelect = (result: SearchResult) => {
    addToRecent(query())
    props.onSelect?.(result)
    result.onClick?.()
    setQuery('')
    setIsOpen(false)
    searchRef?.blur()
  }

  // Add to recent searches
  const addToRecent = (searchQuery: string) => {
    if (!searchQuery.trim()) return
    
    const updated = [searchQuery, ...recentSearches().filter(s => s !== searchQuery)].slice(0, 5)
    setRecentSearches(updated)
    localStorage.setItem('cosmic-search-recent', JSON.stringify(updated))
  }

  // Handle clicks outside
  const handleClickOutside = (e: MouseEvent) => {
    if (resultsRef && !resultsRef.contains(e.target as Node) && 
        searchRef && !searchRef.contains(e.target as Node)) {
      setIsOpen(false)
    }
  }

  onMount(() => {
    document.addEventListener('click', handleClickOutside)
  })

  onCleanup(() => {
    if (debounceTimeout) clearTimeout(debounceTimeout)
    document.removeEventListener('click', handleClickOutside)
  })

  // Group results by category
  const groupedResults = createMemo(() => {
    const groups: Record<string, SearchResult[]> = {}
    results().forEach(result => {
      if (!groups[result.category]) groups[result.category] = []
      groups[result.category].push(result)
    })
    return groups
  })

  const getCategoryIcon = (category: string) => {
    switch (category.toLowerCase()) {
      case 'customers': return Users
      case 'equipment': return Zap
      case 'orders': return Calendar
      case 'contacts': return Mail
      default: return Star
    }
  }

  const getResultIcon = (result: SearchResult) => {
    if (result.icon) return result.icon
    return getCategoryIcon(result.category)
  }

  return (
    <div class={`relative ${props.class || ''}`}>
      {/* Search Input */}
      <div class="relative">
        <Search size={20} class="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50" />
        <input
          ref={searchRef}
          type="text"
          placeholder={placeholder()}
          value={query()}
          onInput={(e) => handleInput(e.target.value)}
          onFocus={() => setIsOpen(true)}
          onKeyDown={handleKeyDown}
          class="w-full bg-white/10 backdrop-blur-lg border border-white/20 rounded-lg pl-10 pr-12 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-cosmic-400 focus:border-cosmic-400 transition-all duration-300"
        />
        
        <Show when={query()}>
          <button
            class="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/50 hover:text-white transition-colors"
            onClick={() => {
              setQuery('')
              setResults([])
              setIsOpen(false)
            }}
          >
            <X size={16} />
          </button>
        </Show>
      </div>

      {/* Search Results Dropdown */}
      <Show when={isOpen()}>
        <Portal>
          <div
            ref={resultsRef}
            class="fixed z-50 mt-2 w-full max-w-2xl bg-gray-900/95 backdrop-blur-xl border border-white/20 rounded-xl shadow-2xl overflow-hidden"
            style={{
              top: `${searchRef?.getBoundingClientRect().bottom! + 8}px`,
              left: `${searchRef?.getBoundingClientRect().left}px`,
              width: `${searchRef?.getBoundingClientRect().width}px`
            }}
          >
            {/* Loading State */}
            <Show when={isLoading()}>
              <div class="p-4 flex items-center space-x-3 text-white/70">
                <div class="animate-spin">
                  <Sparkles size={16} />
                </div>
                <span class="text-sm">Searching...</span>
              </div>
            </Show>

            {/* No Results */}
            <Show when={!isLoading() && query() && results().length === 0}>
              <div class="p-4 text-center text-white/70">
                <div class="mb-2">
                  <Search size={24} class="mx-auto opacity-50" />
                </div>
                <div class="text-sm">No results found for "{query()}"</div>
                <div class="text-xs mt-1 opacity-70">Try different keywords or check spelling</div>
              </div>
            </Show>

            {/* Search Results */}
            <Show when={!isLoading() && results().length > 0}>
              <div class="max-h-96 overflow-y-auto">
                <Show when={props.showCategories}>
                  <For each={Object.entries(groupedResults())}>
                    {([category, categoryResults]) => (
                      <div>
                        <div class="px-4 py-2 bg-white/5 border-b border-white/10">
                          <div class="flex items-center space-x-2 text-white/80 text-xs font-medium uppercase tracking-wide">
                            <div class="w-3 h-3 flex items-center justify-center">
                              {getCategoryIcon(category)({ size: 12 })}
                            </div>
                            <span>{category}</span>
                            <span class="text-white/50">({categoryResults.length})</span>
                          </div>
                        </div>
                        <For each={categoryResults}>
                          {(result, index) => {
                            const globalIndex = Object.entries(groupedResults())
                              .slice(0, Object.keys(groupedResults()).indexOf(category))
                              .reduce((acc, [, results]) => acc + results.length, 0) + index()
                            
                            return (
                              <div
                                class={`px-4 py-3 hover:bg-white/10 cursor-pointer transition-colors border-b border-white/5 last:border-b-0 ${
                                  selectedIndex() === globalIndex ? 'bg-cosmic-500/20' : ''
                                }`}
                                onClick={() => handleSelect(result)}
                              >
                                <div class="flex items-center space-x-3">
                                  <div class="w-8 h-8 bg-gradient-to-r from-cosmic-400 to-divine-400 rounded-lg flex items-center justify-center flex-shrink-0">
                                    {getResultIcon(result)({ size: 16, class: 'text-white' })}
                                  </div>
                                  <div class="flex-1 min-w-0">
                                    <div class="flex items-center space-x-2">
                                      <div class="text-white font-medium truncate">{result.title}</div>
                                      <Show when={result.score}>
                                        <div class="px-1.5 py-0.5 bg-cosmic-400/20 text-cosmic-300 text-xs rounded">
                                          {Math.round(result.score! * 100)}%
                                        </div>
                                      </Show>
                                    </div>
                                    <Show when={result.subtitle}>
                                      <div class="text-white/70 text-sm truncate">{result.subtitle}</div>
                                    </Show>
                                    <Show when={result.description}>
                                      <div class="text-white/50 text-xs truncate mt-1">{result.description}</div>
                                    </Show>
                                  </div>
                                  <ArrowRight size={14} class="text-white/30" />
                                </div>
                              </div>
                            )
                          }}
                        </For>
                      </div>
                    )}
                  </For>
                </Show>

                <Show when={!props.showCategories}>
                  <For each={results()}>
                    {(result, index) => (
                      <div
                        class={`px-4 py-3 hover:bg-white/10 cursor-pointer transition-colors border-b border-white/5 last:border-b-0 ${
                          selectedIndex() === index() ? 'bg-cosmic-500/20' : ''
                        }`}
                        onClick={() => handleSelect(result)}
                      >
                        <div class="flex items-center space-x-3">
                          <div class="w-8 h-8 bg-gradient-to-r from-cosmic-400 to-divine-400 rounded-lg flex items-center justify-center flex-shrink-0">
                            {getResultIcon(result)({ size: 16, class: 'text-white' })}
                          </div>
                          <div class="flex-1 min-w-0">
                            <div class="flex items-center space-x-2">
                              <div class="text-white font-medium truncate">{result.title}</div>
                              <Show when={result.score}>
                                <div class="px-1.5 py-0.5 bg-cosmic-400/20 text-cosmic-300 text-xs rounded">
                                  {Math.round(result.score! * 100)}%
                                </div>
                              </Show>
                            </div>
                            <Show when={result.subtitle}>
                              <div class="text-white/70 text-sm truncate">{result.subtitle}</div>
                            </Show>
                            <Show when={result.description}>
                              <div class="text-white/50 text-xs truncate mt-1">{result.description}</div>
                            </Show>
                          </div>
                          <ArrowRight size={14} class="text-white/30" />
                        </div>
                      </div>
                    )}
                  </For>
                </Show>
              </div>
            </Show>

            {/* Recent Searches */}
            <Show when={props.showRecent && !query() && recentSearches().length > 0}>
              <div>
                <div class="px-4 py-2 bg-white/5 border-b border-white/10">
                  <div class="flex items-center space-x-2 text-white/80 text-xs font-medium uppercase tracking-wide">
                    <Clock size={12} />
                    <span>Recent Searches</span>
                  </div>
                </div>
                <For each={recentSearches()}>
                  {(recent, index) => (
                    <div
                      class={`px-4 py-3 hover:bg-white/10 cursor-pointer transition-colors border-b border-white/5 last:border-b-0 ${
                        selectedIndex() === results().length + index() ? 'bg-cosmic-500/20' : ''
                      }`}
                      onClick={() => {
                        setQuery(recent)
                        debouncedSearch(recent)
                      }}
                    >
                      <div class="flex items-center space-x-3">
                        <Clock size={14} class="text-white/50" />
                        <span class="text-white">{recent}</span>
                      </div>
                    </div>
                  )}
                </For>
              </div>
            </Show>

            {/* Suggestions */}
            <Show when={props.showSuggestions && !query() && props.suggestions?.length}>
              <div>
                <div class="px-4 py-2 bg-white/5 border-b border-white/10">
                  <div class="flex items-center space-x-2 text-white/80 text-xs font-medium uppercase tracking-wide">
                    <TrendingUp size={12} />
                    <span>Popular Searches</span>
                  </div>
                </div>
                <For each={props.suggestions}>
                  {(suggestion) => (
                    <div
                      class="px-4 py-3 hover:bg-white/10 cursor-pointer transition-colors border-b border-white/5 last:border-b-0"
                      onClick={() => {
                        setQuery(suggestion.text)
                        debouncedSearch(suggestion.text)
                      }}
                    >
                      <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                          <TrendingUp size={14} class="text-white/50" />
                          <span class="text-white">{suggestion.text}</span>
                        </div>
                        <Show when={suggestion.count}>
                          <span class="text-white/50 text-xs">{suggestion.count}</span>
                        </Show>
                      </div>
                    </div>
                  )}
                </For>
              </div>
            </Show>
          </div>
        </Portal>
      </Show>
    </div>
  )
}
