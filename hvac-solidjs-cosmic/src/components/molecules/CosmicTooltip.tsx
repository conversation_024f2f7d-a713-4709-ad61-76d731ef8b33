import { type Component, type JSX, createSignal, onMount, onCleanup, Show } from 'solid-js'
import { Portal } from 'solid-js/web'

export interface TooltipProps {
  content: string | JSX.Element
  children: JSX.Element
  position?: 'top' | 'bottom' | 'left' | 'right' | 'auto'
  delay?: number
  offset?: number
  interactive?: boolean
  maxWidth?: number
  theme?: 'dark' | 'light' | 'cosmic'
  showArrow?: boolean
  animation?: 'fade' | 'scale' | 'slide' | 'bounce'
  trigger?: 'hover' | 'click' | 'focus'
}

export const CosmicTooltip: Component<TooltipProps> = (props) => {
  const [isVisible, setIsVisible] = createSignal(false)
  const [position, setPosition] = createSignal({ x: 0, y: 0 })
  const [actualPosition, setActualPosition] = createSignal(props.position || 'top')
  
  let triggerRef: HTMLElement | undefined
  let tooltipRef: HTMLDivElement | undefined
  let timeoutId: number | undefined

  const delay = () => props.delay || 200
  const offset = () => props.offset || 8
  const maxWidth = () => props.maxWidth || 300
  const theme = () => props.theme || 'cosmic'
  const showArrow = () => props.showArrow !== false
  const animation = () => props.animation || 'fade'
  const trigger = () => props.trigger || 'hover'

  const calculatePosition = () => {
    if (!triggerRef || !tooltipRef) return

    const triggerRect = triggerRef.getBoundingClientRect()
    const tooltipRect = tooltipRef.getBoundingClientRect()
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    }

    let pos = props.position || 'top'
    let x = 0
    let y = 0

    // Auto-position if needed
    if (pos === 'auto') {
      const spaceTop = triggerRect.top
      const spaceBottom = viewport.height - triggerRect.bottom
      const spaceLeft = triggerRect.left
      const spaceRight = viewport.width - triggerRect.right

      if (spaceTop >= tooltipRect.height + offset()) {
        pos = 'top'
      } else if (spaceBottom >= tooltipRect.height + offset()) {
        pos = 'bottom'
      } else if (spaceRight >= tooltipRect.width + offset()) {
        pos = 'right'
      } else {
        pos = 'left'
      }
    }

    // Calculate position based on final position
    switch (pos) {
      case 'top':
        x = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2
        y = triggerRect.top - tooltipRect.height - offset()
        break
      case 'bottom':
        x = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2
        y = triggerRect.bottom + offset()
        break
      case 'left':
        x = triggerRect.left - tooltipRect.width - offset()
        y = triggerRect.top + triggerRect.height / 2 - tooltipRect.height / 2
        break
      case 'right':
        x = triggerRect.right + offset()
        y = triggerRect.top + triggerRect.height / 2 - tooltipRect.height / 2
        break
    }

    // Keep tooltip within viewport
    x = Math.max(8, Math.min(x, viewport.width - tooltipRect.width - 8))
    y = Math.max(8, Math.min(y, viewport.height - tooltipRect.height - 8))

    setPosition({ x, y })
    setActualPosition(pos)
  }

  const showTooltip = () => {
    if (timeoutId) clearTimeout(timeoutId)
    timeoutId = setTimeout(() => {
      setIsVisible(true)
      setTimeout(calculatePosition, 0)
    }, delay()) as unknown as number
  }

  const hideTooltip = () => {
    if (timeoutId) clearTimeout(timeoutId)
    setIsVisible(false)
  }

  const handleTriggerEvents = () => {
    if (!triggerRef) return

    if (trigger() === 'hover') {
      triggerRef.addEventListener('mouseenter', showTooltip)
      triggerRef.addEventListener('mouseleave', hideTooltip)
    } else if (trigger() === 'click') {
      triggerRef.addEventListener('click', () => {
        if (isVisible()) {
          hideTooltip()
        } else {
          showTooltip()
        }
      })
    } else if (trigger() === 'focus') {
      triggerRef.addEventListener('focus', showTooltip)
      triggerRef.addEventListener('blur', hideTooltip)
    }
  }

  onMount(() => {
    handleTriggerEvents()
    window.addEventListener('scroll', calculatePosition)
    window.addEventListener('resize', calculatePosition)
  })

  onCleanup(() => {
    if (timeoutId) clearTimeout(timeoutId)
    if (triggerRef) {
      triggerRef.removeEventListener('mouseenter', showTooltip)
      triggerRef.removeEventListener('mouseleave', hideTooltip)
      triggerRef.removeEventListener('click', showTooltip)
      triggerRef.removeEventListener('focus', showTooltip)
      triggerRef.removeEventListener('blur', hideTooltip)
    }
    window.removeEventListener('scroll', calculatePosition)
    window.removeEventListener('resize', calculatePosition)
  })

  const getThemeClasses = () => {
    switch (theme()) {
      case 'dark':
        return 'bg-gray-900 text-white border-gray-700'
      case 'light':
        return 'bg-white text-gray-900 border-gray-200 shadow-lg'
      case 'cosmic':
      default:
        return 'bg-gradient-to-r from-cosmic-600 to-divine-600 text-white border-cosmic-400/30 shadow-2xl'
    }
  }

  const getAnimationClasses = () => {
    const base = 'transition-all duration-300 ease-out'
    if (!isVisible()) {
      switch (animation()) {
        case 'scale':
          return `${base} opacity-0 scale-75`
        case 'slide':
          return `${base} opacity-0 ${
            actualPosition() === 'top' ? 'translate-y-2' :
            actualPosition() === 'bottom' ? '-translate-y-2' :
            actualPosition() === 'left' ? 'translate-x-2' :
            '-translate-x-2'
          }`
        case 'bounce':
          return `${base} opacity-0 scale-75 animate-bounce`
        default:
          return `${base} opacity-0`
      }
    }
    return `${base} opacity-100 scale-100 translate-x-0 translate-y-0`
  }

  const getArrowClasses = () => {
    const baseArrow = 'absolute w-2 h-2 transform rotate-45'
    const themeArrow = theme() === 'cosmic' 
      ? 'bg-gradient-to-r from-cosmic-600 to-divine-600 border-cosmic-400/30'
      : theme() === 'dark'
      ? 'bg-gray-900 border-gray-700'
      : 'bg-white border-gray-200'

    switch (actualPosition()) {
      case 'top':
        return `${baseArrow} ${themeArrow} -bottom-1 left-1/2 -translate-x-1/2 border-b border-r`
      case 'bottom':
        return `${baseArrow} ${themeArrow} -top-1 left-1/2 -translate-x-1/2 border-t border-l`
      case 'left':
        return `${baseArrow} ${themeArrow} -right-1 top-1/2 -translate-y-1/2 border-t border-r`
      case 'right':
        return `${baseArrow} ${themeArrow} -left-1 top-1/2 -translate-y-1/2 border-b border-l`
      default:
        return `${baseArrow} ${themeArrow}`
    }
  }

  return (
    <>
      <div ref={triggerRef} class="inline-block">
        {props.children}
      </div>
      
      <Show when={isVisible()}>
        <Portal>
          <div
            ref={tooltipRef}
            class={`
              fixed z-50 px-3 py-2 text-sm rounded-lg border backdrop-blur-lg
              ${getThemeClasses()}
              ${getAnimationClasses()}
            `}
            style={{
              left: `${position().x}px`,
              top: `${position().y}px`,
              'max-width': `${maxWidth()}px`,
              'pointer-events': props.interactive ? 'auto' : 'none'
            }}
            onMouseEnter={() => props.interactive && trigger() === 'hover' && showTooltip()}
            onMouseLeave={() => props.interactive && trigger() === 'hover' && hideTooltip()}
          >
            {typeof props.content === 'string' ? (
              <div innerHTML={props.content} />
            ) : (
              props.content
            )}
            
            <Show when={showArrow()}>
              <div class={getArrowClasses()} />
            </Show>
          </div>
        </Portal>
      </Show>
    </>
  )
}

// Rich Tooltip with Icons and Actions
export const RichTooltip: Component<{
  title?: string
  description?: string
  icon?: JSX.Element
  actions?: Array<{
    label: string
    onClick: () => void
    variant?: 'primary' | 'secondary'
  }>
  children: JSX.Element
  position?: TooltipProps['position']
  theme?: TooltipProps['theme']
}> = (props) => {
  const content = (
    <div class="space-y-2 min-w-0">
      <Show when={props.title}>
        <div class="flex items-center space-x-2">
          <Show when={props.icon}>
            <div class="flex-shrink-0">{props.icon}</div>
          </Show>
          <div class="font-semibold text-sm">{props.title}</div>
        </div>
      </Show>
      
      <Show when={props.description}>
        <div class="text-xs opacity-90 leading-relaxed">
          {props.description}
        </div>
      </Show>
      
      <Show when={props.actions && props.actions.length > 0}>
        <div class="flex space-x-2 pt-1">
          {props.actions!.map((action) => (
            <button
              class={`px-2 py-1 text-xs rounded transition-colors ${
                action.variant === 'primary'
                  ? 'bg-white/20 hover:bg-white/30 text-white'
                  : 'bg-white/10 hover:bg-white/20 text-white/80'
              }`}
              onClick={action.onClick}
            >
              {action.label}
            </button>
          ))}
        </div>
      </Show>
    </div>
  )

  return (
    <CosmicTooltip
      content={content}
      position={props.position}
      theme={props.theme}
      interactive={!!props.actions}
      maxWidth={280}
    >
      {props.children}
    </CosmicTooltip>
  )
}
