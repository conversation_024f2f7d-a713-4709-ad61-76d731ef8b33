import { type Component, createSignal, onMount, Show, For } from 'solid-js'
import { Portal } from 'solid-js/web'
import { CosmicCard } from '../atoms/CosmicCard'
import { GoldenButton } from '../atoms/GoldenButton'
import { FloatingParticles, GlowEffect, RippleEffect } from '../atoms/CosmicAnimations'
import { CosmicTooltip, RichTooltip } from '../molecules/CosmicTooltip'
import {
  X,
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Star,
  Activity,
  Brain,
  Heart,
  Zap,
  Award,
  TrendingUp,
  Clock,
  MessageCircle,
  Video,
  Edit,
  Share2,
  Download,
  Settings,
  Tag,
  Building,
  Globe,
  Shield,
  Bookmark,
  BarChart3,
  PieChart,
  Target,
  Sparkles
} from 'lucide-solid'

export interface ImmersiveCustomerProfileProps {
  customerId: string | null
  isOpen: boolean
  onClose: () => void
}

export const ImmersiveCustomerProfile: Component<ImmersiveCustomerProfileProps> = (props) => {
  const [isLoaded, setIsLoaded] = createSignal(false)
  const [activeTab, setActiveTab] = createSignal('overview')
  const [showParticles, setShowParticles] = createSignal(true)

  // Mock customer data - in real app this would come from API
  const customer = () => ({
    id: props.customerId || '1',
    name: 'Acme Corporation',
    email: '<EMAIL>',
    phone: '+****************',
    address: '123 Business Ave, Tech City, TC 12345',
    status: 'active',
    avatar: 'AC',
    score: 87,
    tags: ['Premium', 'Enterprise', 'High-Value'],
    joinDate: '2023-01-15',
    lastContact: '2024-01-10',
    totalOrders: 24,
    totalValue: 125000,
    satisfaction: 4.8,
    projects: [
      { id: '1', name: 'HVAC System Installation', status: 'completed', value: 45000 },
      { id: '2', name: 'Maintenance Contract', status: 'active', value: 12000 },
      { id: '3', name: 'Emergency Repair', status: 'pending', value: 3500 }
    ],
    equipment: [
      { id: '1', type: 'Central AC Unit', model: 'Carrier 24ABC6', status: 'operational' },
      { id: '2', type: 'Heat Pump', model: 'Trane XR15', status: 'maintenance' },
      { id: '3', type: 'Air Handler', model: 'Goodman ARUF', status: 'operational' }
    ],
    timeline: [
      { date: '2024-01-10', type: 'contact', description: 'Phone call regarding maintenance' },
      { date: '2024-01-05', type: 'service', description: 'Completed routine maintenance' },
      { date: '2023-12-20', type: 'payment', description: 'Payment received - $12,000' },
      { date: '2023-12-15', type: 'project', description: 'Started new HVAC installation' }
    ]
  })

  onMount(() => {
    if (props.isOpen) {
      setTimeout(() => setIsLoaded(true), 100)
    }
  })

  const tabs = [
    { id: 'overview', label: 'Overview', icon: User },
    { id: 'projects', label: 'Projects', icon: Building },
    { id: 'equipment', label: 'Equipment', icon: Zap },
    { id: 'timeline', label: 'Timeline', icon: Clock },
    { id: 'analytics', label: 'Analytics', icon: BarChart3 }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-400'
      case 'completed': return 'text-blue-400'
      case 'pending': return 'text-yellow-400'
      case 'maintenance': return 'text-orange-400'
      default: return 'text-gray-400'
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  return (
    <Show when={props.isOpen && props.customerId}>
      <Portal>
        <div class="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* Backdrop */}
          <div 
            class="absolute inset-0 bg-black/70 backdrop-blur-md transition-opacity duration-500"
            onClick={props.onClose}
          />
          
          {/* Modal */}
          <div class={`relative w-full max-w-6xl h-[90vh] transition-all duration-700 ${
            isLoaded() ? 'opacity-100 scale-100' : 'opacity-0 scale-95'
          }`}>
            <CosmicCard variant="cosmic" size="lg" glow class="h-full overflow-hidden">
              {/* Floating Particles */}
              <Show when={showParticles()}>
                <FloatingParticles 
                  count={8} 
                  color="rgba(139, 92, 246, 0.15)" 
                  size={2} 
                  speed={1} 
                />
              </Show>

              {/* Header */}
              <div class="flex items-center justify-between p-6 border-b border-white/10">
                <div class="flex items-center space-x-4">
                  {/* Avatar */}
                  <GlowEffect color="#8b5cf6" intensity={20}>
                    <div class="w-16 h-16 bg-gradient-to-r from-cosmic-400 to-divine-400 rounded-full flex items-center justify-center shadow-2xl">
                      <span class="text-white text-xl font-bold">{customer().avatar}</span>
                    </div>
                  </GlowEffect>
                  
                  {/* Customer Info */}
                  <div>
                    <div class="flex items-center space-x-3">
                      <h2 class="text-2xl font-bold text-white">{customer().name}</h2>
                      <div class="flex items-center space-x-1">
                        <Brain size={16} class="text-purple-400" />
                        <span class="text-purple-400 font-bold">{customer().score}</span>
                      </div>
                    </div>
                    <div class="flex items-center space-x-4 mt-1">
                      <span class={`text-sm ${getStatusColor(customer().status)}`}>
                        {customer().status.toUpperCase()}
                      </span>
                      <span class="text-white/70 text-sm">
                        Customer since {formatDate(customer().joinDate)}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Header Actions */}
                <div class="flex items-center space-x-2">
                  <CosmicTooltip content="Toggle particles" position="bottom">
                    <GoldenButton
                      variant={showParticles() ? 'cosmic' : 'glass'}
                      size="sm"
                      glow={showParticles()}
                      onClick={() => setShowParticles(!showParticles())}
                    >
                      <Sparkles size={16} />
                    </GoldenButton>
                  </CosmicTooltip>

                  <RichTooltip
                    title="Quick Actions"
                    description="Perform common actions for this customer"
                    actions={[
                      { label: 'Call', onClick: () => console.log('Call customer') },
                      { label: 'Email', onClick: () => console.log('Email customer') }
                    ]}
                  >
                    <GoldenButton variant="divine" size="sm" glow>
                      <Settings size={16} />
                    </GoldenButton>
                  </RichTooltip>

                  <GoldenButton
                    variant="glass"
                    size="sm"
                    glow
                    onClick={props.onClose}
                  >
                    <X size={16} />
                  </GoldenButton>
                </div>
              </div>

              {/* Tabs */}
              <div class="flex border-b border-white/10 px-6">
                <For each={tabs}>
                  {(tab) => {
                    const TabIcon = tab.icon
                    return (
                      <RippleEffect color="rgba(139, 92, 246, 0.3)">
                        <button
                          class={`flex items-center space-x-2 px-4 py-3 transition-all duration-300 border-b-2 ${
                            activeTab() === tab.id
                              ? 'border-cosmic-400 text-cosmic-400'
                              : 'border-transparent text-white/70 hover:text-white hover:border-white/20'
                          }`}
                          onClick={() => setActiveTab(tab.id)}
                        >
                          <TabIcon size={16} />
                          <span class="font-medium">{tab.label}</span>
                        </button>
                      </RippleEffect>
                    )
                  }}
                </For>
              </div>

              {/* Content */}
              <div class="flex-1 overflow-y-auto p-6">
                {/* Overview Tab */}
                <Show when={activeTab() === 'overview'}>
                  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Contact Information */}
                    <div class="lg:col-span-2 space-y-6">
                      <CosmicCard variant="glass" size="md" glow>
                        <h3 class="text-lg font-bold text-white mb-4 flex items-center space-x-2">
                          <User size={20} />
                          <span>Contact Information</span>
                        </h3>
                        
                        <div class="space-y-4">
                          <div class="flex items-center space-x-3">
                            <Mail size={16} class="text-cosmic-400" />
                            <span class="text-white">{customer().email}</span>
                            <CosmicTooltip content="Copy email">
                              <button class="text-white/50 hover:text-white transition-colors">
                                <Share2 size={14} />
                              </button>
                            </CosmicTooltip>
                          </div>
                          
                          <div class="flex items-center space-x-3">
                            <Phone size={16} class="text-golden-400" />
                            <span class="text-white">{customer().phone}</span>
                            <CosmicTooltip content="Call customer">
                              <button class="text-white/50 hover:text-white transition-colors">
                                <Phone size={14} />
                              </button>
                            </CosmicTooltip>
                          </div>
                          
                          <div class="flex items-center space-x-3">
                            <MapPin size={16} class="text-divine-400" />
                            <span class="text-white">{customer().address}</span>
                            <CosmicTooltip content="View on map">
                              <button class="text-white/50 hover:text-white transition-colors">
                                <Globe size={14} />
                              </button>
                            </CosmicTooltip>
                          </div>
                        </div>
                      </CosmicCard>

                      {/* Tags */}
                      <CosmicCard variant="glass" size="md" glow>
                        <h3 class="text-lg font-bold text-white mb-4 flex items-center space-x-2">
                          <Tag size={20} />
                          <span>Tags</span>
                        </h3>
                        
                        <div class="flex flex-wrap gap-2">
                          <For each={customer().tags}>
                            {(tag) => (
                              <span class="px-3 py-1 bg-cosmic-500/20 text-cosmic-300 text-sm rounded-full border border-cosmic-400/30">
                                {tag}
                              </span>
                            )}
                          </For>
                          <button class="px-3 py-1 bg-white/10 text-white/70 text-sm rounded-full border border-white/20 hover:bg-white/20 transition-colors">
                            + Add Tag
                          </button>
                        </div>
                      </CosmicCard>
                    </div>

                    {/* Stats */}
                    <div class="space-y-4">
                      <CosmicCard variant="cosmic" size="md" glow>
                        <div class="text-center">
                          <div class="text-3xl font-bold text-white mb-2">
                            {formatCurrency(customer().totalValue)}
                          </div>
                          <div class="text-white/70 text-sm">Total Value</div>
                          <div class="flex items-center justify-center space-x-1 mt-2">
                            <TrendingUp size={12} class="text-green-400" />
                            <span class="text-green-400 text-xs">+15.3%</span>
                          </div>
                        </div>
                      </CosmicCard>

                      <CosmicCard variant="golden" size="md" glow>
                        <div class="text-center">
                          <div class="text-3xl font-bold text-white mb-2">
                            {customer().totalOrders}
                          </div>
                          <div class="text-white/70 text-sm">Total Orders</div>
                          <div class="flex items-center justify-center space-x-1 mt-2">
                            <Activity size={12} class="text-blue-400" />
                            <span class="text-blue-400 text-xs">Active</span>
                          </div>
                        </div>
                      </CosmicCard>

                      <CosmicCard variant="divine" size="md" glow>
                        <div class="text-center">
                          <div class="flex items-center justify-center space-x-1 mb-2">
                            <For each={Array.from({ length: 5 }, (_, i) => i < Math.floor(customer().satisfaction))}>
                              {() => <Star size={16} class="text-yellow-400 fill-current" />}
                            </For>
                          </div>
                          <div class="text-2xl font-bold text-white mb-2">
                            {customer().satisfaction}
                          </div>
                          <div class="text-white/70 text-sm">Satisfaction</div>
                        </div>
                      </CosmicCard>
                    </div>
                  </div>
                </Show>

                {/* Other tabs content would go here */}
                <Show when={activeTab() !== 'overview'}>
                  <div class="text-center py-12">
                    <div class="text-white/50 text-lg">
                      {tabs.find(t => t.id === activeTab())?.label} content coming soon...
                    </div>
                  </div>
                </Show>
              </div>
            </CosmicCard>
          </div>
        </div>
      </Portal>
    </Show>
  )
}
